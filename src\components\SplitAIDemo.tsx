/**
 * Demo component showing the split AI system in action
 * 
 * Phase 1: Fast roadmap generation with lesson plans only
 * Phase 2: On-demand lesson content generation
 */

import React, { useState } from 'react';
import { aiService } from '../services/aiService';
import { splitAIService } from '../services/splitAIService';
import type { UserPreferences } from '../services/aiService';
import type { RoadmapOutline, LessonPlan, FullLesson } from '../services/splitAIService';

interface SplitAIDemoProps {
  onClose?: () => void;
}

export const SplitAIDemo: React.FC<SplitAIDemoProps> = ({ onClose }) => {
  const [roadmapOutline, setRoadmapOutline] = useState<RoadmapOutline | null>(null);
  const [generatedLessons, setGeneratedLessons] = useState<Map<string, FullLesson>>(new Map());
  const [isGeneratingRoadmap, setIsGeneratingRoadmap] = useState(false);
  const [generatingLessonId, setGeneratingLessonId] = useState<string | null>(null);
  const [selectedModule, setSelectedModule] = useState<string | null>(null);
  const [performanceStats, setPerformanceStats] = useState<{
    roadmapTime: number;
    lessonTimes: Map<string, number>;
  }>({
    roadmapTime: 0,
    lessonTimes: new Map()
  });

  const samplePreferences: UserPreferences = {
    currentLevel: 'absolute-beginner',
    learningGoal: 'travel',
    timeCommitment: '30-60 minutes daily',
    focusAreas: ['speaking', 'reading'],
    learningStyle: 'visual'
  };

  const generateRoadmapOutline = async () => {
    setIsGeneratingRoadmap(true);
    const startTime = Date.now();
    
    try {
      console.log('🚀 Phase 1: Generating roadmap with lesson PLANS only (fast)...');
      const outline = await splitAIService.generateRoadmapWithPlans(samplePreferences);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      setRoadmapOutline(outline);
      setPerformanceStats(prev => ({
        ...prev,
        roadmapTime: duration
      }));
      
      console.log(`✅ Phase 1 completed in ${duration}ms`);
      console.log(`📋 Generated ${outline.modules.length} modules with ${outline.modules.reduce((sum, mod) => sum + mod.lessonPlans.length, 0)} lesson plans`);
      
    } catch (error) {
      console.error('❌ Phase 1 failed:', error);
    } finally {
      setIsGeneratingRoadmap(false);
    }
  };

  const generateLessonContent = async (lessonPlan: LessonPlan, moduleTitle: string, moduleDescription: string, moduleContext: any) => {
    if (generatedLessons.has(lessonPlan.id)) {
      console.log(`📖 Lesson "${lessonPlan.title}" already generated`);
      return;
    }

    setGeneratingLessonId(lessonPlan.id);
    const startTime = Date.now();
    
    try {
      console.log(`🎯 Phase 2: Generating full content for lesson "${lessonPlan.title}"...`);
      
      const fullLesson = await splitAIService.generateLessonContent(
        lessonPlan,
        {
          title: moduleTitle,
          description: moduleDescription,
          contentContext: moduleContext
        },
        samplePreferences.currentLevel
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      setGeneratedLessons(prev => new Map(prev.set(lessonPlan.id, fullLesson)));
      setPerformanceStats(prev => ({
        ...prev,
        lessonTimes: new Map(prev.lessonTimes.set(lessonPlan.id, duration))
      }));
      
      console.log(`✅ Phase 2 completed in ${duration}ms`);
      console.log(`📚 Generated full content: ${fullLesson.content.exercises.length} exercises, ${fullLesson.content.vocabulary?.length || 0} vocabulary items`);
      
    } catch (error) {
      console.error(`❌ Phase 2 failed for lesson "${lessonPlan.title}":`, error);
    } finally {
      setGeneratingLessonId(null);
    }
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Split AI System Demo</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-xl"
            >
              ×
            </button>
          )}
        </div>

        <div className="space-y-6">
          {/* Phase 1: Roadmap Generation */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 text-blue-600">
              🚀 Phase 1: Fast Roadmap Generation (Plans Only)
            </h3>
            <p className="text-gray-600 mb-4">
              Generates roadmap structure with detailed lesson plans but no full content. Much faster than the original approach.
            </p>
            
            <button
              onClick={generateRoadmapOutline}
              disabled={isGeneratingRoadmap}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isGeneratingRoadmap ? 'Generating Roadmap...' : 'Generate Roadmap with Plans'}
            </button>

            {performanceStats.roadmapTime > 0 && (
              <div className="mt-2 text-sm text-green-600">
                ✅ Completed in {formatTime(performanceStats.roadmapTime)}
              </div>
            )}
          </div>

          {/* Roadmap Results */}
          {roadmapOutline && (
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3 text-green-600">
                📋 Generated Roadmap Structure
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {roadmapOutline.modules.map((module, index) => (
                  <div
                    key={module.id}
                    className={`border rounded p-3 cursor-pointer transition-colors ${
                      selectedModule === module.id ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedModule(selectedModule === module.id ? null : module.id)}
                  >
                    <h4 className="font-medium text-gray-800">{module.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{module.description}</p>
                    <div className="text-xs text-gray-500 mt-2">
                      {module.lessonPlans.length} lesson plans • {module.duration}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Phase 2: Lesson Content Generation */}
          {selectedModule && roadmapOutline && (
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3 text-purple-600">
                🎯 Phase 2: On-Demand Lesson Content Generation
              </h3>
              <p className="text-gray-600 mb-4">
                Click on any lesson plan to generate its full content. This happens only when needed.
              </p>

              {(() => {
                const module = roadmapOutline.modules.find(m => m.id === selectedModule);
                if (!module) return null;

                return (
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-800">
                      Lessons in "{module.title}"
                    </h4>
                    {module.lessonPlans.map((lessonPlan) => {
                      const isGenerated = generatedLessons.has(lessonPlan.id);
                      const isGenerating = generatingLessonId === lessonPlan.id;
                      const generationTime = performanceStats.lessonTimes.get(lessonPlan.id);

                      return (
                        <div
                          key={lessonPlan.id}
                          className="border rounded p-3 space-y-2"
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h5 className="font-medium text-gray-800">{lessonPlan.title}</h5>
                              <p className="text-sm text-gray-600">
                                {lessonPlan.learningObjectives.join(', ')}
                              </p>
                              <div className="text-xs text-gray-500 mt-1">
                                Duration: {lessonPlan.estimatedDuration} • 
                                Difficulty: {lessonPlan.difficultyLevel}/5 •
                                Vocabulary: {lessonPlan.vocabularyFocus.length} items •
                                Grammar: {lessonPlan.grammarFocus.length} points
                              </div>
                            </div>
                            <button
                              onClick={() => generateLessonContent(
                                lessonPlan,
                                module.title,
                                module.description,
                                module.contentContext
                              )}
                              disabled={isGenerating || isGenerated}
                              className={`px-3 py-1 rounded text-sm ${
                                isGenerated
                                  ? 'bg-green-100 text-green-700'
                                  : isGenerating
                                  ? 'bg-yellow-100 text-yellow-700'
                                  : 'bg-purple-500 text-white hover:bg-purple-600'
                              }`}
                            >
                              {isGenerated ? '✅ Generated' : isGenerating ? '⏳ Generating...' : 'Generate Content'}
                            </button>
                          </div>

                          {generationTime && (
                            <div className="text-xs text-green-600">
                              Generated in {formatTime(generationTime)}
                            </div>
                          )}

                          {isGenerated && (() => {
                            const fullLesson = generatedLessons.get(lessonPlan.id);
                            if (!fullLesson) return null;

                            return (
                              <div className="bg-gray-50 rounded p-3 mt-2">
                                <h6 className="font-medium text-gray-700 mb-2">Generated Content:</h6>
                                <div className="text-sm space-y-1">
                                  <div>📝 Exercises: {fullLesson.content.exercises.length}</div>
                                  <div>📚 Vocabulary: {fullLesson.content.vocabulary?.length || 0} items</div>
                                  <div>📖 Grammar: {fullLesson.content.grammar?.length || 0} patterns</div>
                                  <div>💡 Examples: {fullLesson.content.examples.length}</div>
                                </div>
                                <div className="mt-2 text-xs text-gray-600">
                                  Content length: {JSON.stringify(fullLesson.content).length} characters
                                </div>
                              </div>
                            );
                          })()}
                        </div>
                      );
                    })}
                  </div>
                );
              })()}
            </div>
          )}

          {/* Performance Summary */}
          {(performanceStats.roadmapTime > 0 || performanceStats.lessonTimes.size > 0) && (
            <div className="border rounded-lg p-4 bg-gray-50">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                ⚡ Performance Summary
              </h3>
              <div className="space-y-2 text-sm">
                {performanceStats.roadmapTime > 0 && (
                  <div>
                    <strong>Roadmap Generation:</strong> {formatTime(performanceStats.roadmapTime)}
                    {roadmapOutline && (
                      <span className="text-gray-600">
                        {' '}({roadmapOutline.modules.reduce((sum, mod) => sum + mod.lessonPlans.length, 0)} lesson plans)
                      </span>
                    )}
                  </div>
                )}
                {performanceStats.lessonTimes.size > 0 && (
                  <div>
                    <strong>Lesson Content Generation:</strong>
                    <ul className="ml-4 mt-1">
                      {Array.from(performanceStats.lessonTimes.entries()).map(([lessonId, time]) => {
                        const lesson = generatedLessons.get(lessonId);
                        return (
                          <li key={lessonId} className="text-gray-600">
                            {lesson?.title || lessonId}: {formatTime(time)}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}
                <div className="pt-2 border-t">
                  <strong className="text-green-600">
                    Benefits: Fast initial roadmap, content generated only when needed, better user experience
                  </strong>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SplitAIDemo;
