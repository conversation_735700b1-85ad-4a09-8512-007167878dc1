import { z } from 'zod';
import { aiLogger } from '../utils/aiLogger';

// Schema for full lesson content (generated on-demand)
const FullLessonContentSchema = z.object({
  lessonId: z.string(),
  title: z.string(),
  type: z.enum(['reading', 'practice', 'quiz', 'video', 'conversation']),
  duration: z.string(),
  content: z.object({
    introduction: z.string(),
    mainContent: z.object({
      explanation: z.string(),
      examples: z.array(z.object({
        japanese: z.string(),
        romaji: z.string().optional(),
        english: z.string(),
        context: z.string().optional()
      })),
      keyPoints: z.array(z.string())
    }),
    vocabulary: z.array(z.object({
      word: z.string(),
      reading: z.string(),
      meaning: z.string(),
      example: z.string(),
      notes: z.string().optional()
    })).optional(),
    grammar: z.array(z.object({
      pattern: z.string(),
      meaning: z.string(),
      usage: z.string(),
      examples: z.array(z.object({
        japanese: z.string(),
        english: z.string()
      })),
      notes: z.string().optional()
    })).optional(),
    exercises: z.array(z.object({
      id: z.string(),
      type: z.enum(['multiple-choice', 'fill-blank', 'translation', 'listening', 'speaking', 'writing']),
      question: z.string(),
      options: z.array(z.string()).optional(),
      correctAnswer: z.string(),
      explanation: z.string(),
      difficulty: z.number().min(1).max(5)
    })),
    culturalNotes: z.array(z.object({
      topic: z.string(),
      explanation: z.string(),
      examples: z.array(z.string())
    })).optional(),
    practiceActivities: z.array(z.object({
      name: z.string(),
      description: z.string(),
      instructions: z.array(z.string()),
      timeEstimate: z.string()
    }))
  }),
  assessment: z.object({
    completionCriteria: z.array(z.string()),
    masteryIndicators: z.array(z.string()),
    nextSteps: z.array(z.string())
  }),
  resources: z.object({
    additionalReading: z.array(z.string()).optional(),
    audioFiles: z.array(z.string()).optional(),
    videoLinks: z.array(z.string()).optional(),
    practiceLinks: z.array(z.string()).optional()
  })
});

export type FullLessonContent = z.infer<typeof FullLessonContentSchema>;

// Interface for lesson plan from progressive roadmap
export interface LessonPlan {
  id: string;
  title: string;
  type: 'reading' | 'practice' | 'quiz' | 'video' | 'conversation';
  duration: string;
  learningObjectives: string[];
  skillTargets: string[];
  contentThemes: string[];
  vocabularyFocus: string[];
  grammarFocus: string[];
  culturalElements: string[];
  practiceActivities: string[];
  assessmentMethods: string[];
  prerequisites: string[];
  difficultyLevel: number;
  estimatedDuration: string;
}

export interface ModuleContext {
  moduleId: string;
  moduleTitle: string;
  moduleDescription: string;
  contentContext: {
    vocabularyThemes: string[];
    grammarFocusAreas: string[];
    culturalElements: string[];
    practiceTypes: string[];
  };
}

export interface UserProgress {
  completedLessons: string[];
  skillMastery: Record<string, number>;
  strugglingAreas: string[];
  strongAreas: string[];
  learningVelocity: number;
  difficultyPreference: 'easier' | 'standard' | 'challenging';
}

export class OnDemandLessonService {
  private apiKey: string;
  private apiUrl: string;

  constructor() {
    this.apiKey = import.meta.env.VITE_ZHIPU_API_KEY || '';
    this.apiUrl = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
  }

  /**
   * Generate full lesson content on-demand using existing lesson plan
   */
  async generateLessonContent(
    lessonPlan: LessonPlan,
    moduleContext: ModuleContext,
    userProgress: UserProgress,
    previousLessons?: LessonPlan[]
  ): Promise<FullLessonContent> {
    const requestId = aiLogger.startRequest('OnDemandLessonService', 'generateLessonContent', {
      model: 'glm-4.5-x',
      temperature: 0.7,
      maxTokens: 64000,
      lessonId: lessonPlan.id
    });

    try {
      aiLogger.info('OnDemandLessonService', 'generateLessonContent', 'Generating full lesson content from existing plan', {
        requestId,
        lessonId: lessonPlan.id,
        lessonTitle: lessonPlan.title,
        moduleTitle: moduleContext.moduleTitle,
        userProgress: {
          completedLessons: userProgress.completedLessons.length,
          strugglingAreas: userProgress.strugglingAreas,
          difficultyPreference: userProgress.difficultyPreference
        }
      });

      if (!this.apiKey) {
        aiLogger.warn('OnDemandLessonService', 'generateLessonContent', 'API key not found, using fallback', { requestId });
        return this.generateFallbackContent(lessonPlan, moduleContext);
      }

      const prompt = this.createLessonContentPrompt(lessonPlan, moduleContext, userProgress, previousLessons);

      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'glm-4.5-x',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 64000
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const aiContent = data.choices[0]?.message?.content;

      if (!aiContent) {
        throw new Error('No content received from AI');
      }

      const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      const validatedResponse = FullLessonContentSchema.parse(parsedResponse);

      aiLogger.endRequest(requestId, JSON.stringify(validatedResponse), {
        lessonId: validatedResponse.lessonId,
        exerciseCount: validatedResponse.content.exercises.length,
        vocabularyCount: validatedResponse.content.vocabulary?.length || 0,
        grammarCount: validatedResponse.content.grammar?.length || 0
      });

      aiLogger.info('OnDemandLessonService', 'generateLessonContent', 'Lesson content generated successfully', {
        requestId,
        lessonId: validatedResponse.lessonId,
        contentType: validatedResponse.type,
        exerciseCount: validatedResponse.content.exercises.length
      });

      return validatedResponse;

    } catch (error) {
      aiLogger.endRequestWithError(requestId, error as Error, true);
      aiLogger.error('OnDemandLessonService', 'generateLessonContent', 'Failed to generate lesson content', {
        requestId,
        lessonId: lessonPlan.id,
        error: error
      });
      
      return this.generateFallbackContent(lessonPlan, moduleContext);
    }
  }

  private createLessonContentPrompt(
    lessonPlan: LessonPlan,
    moduleContext: ModuleContext,
    userProgress: UserProgress,
    previousLessons?: LessonPlan[]
  ): string {
    const previousContext = previousLessons && previousLessons.length > 0 
      ? `\n**Previous Lessons Context:**\n${previousLessons.map(lesson => 
          `- ${lesson.title}: ${lesson.learningObjectives.join(', ')}`
        ).join('\n')}`
      : '';

    return `Generate comprehensive lesson content based on this existing lesson plan and user progress:

**Lesson Plan Details:**
- ID: ${lessonPlan.id}
- Title: ${lessonPlan.title}
- Type: ${lessonPlan.type}
- Duration: ${lessonPlan.duration}
- Difficulty Level: ${lessonPlan.difficultyLevel}/5
- Learning Objectives: ${lessonPlan.learningObjectives.join(', ')}
- Skill Targets: ${lessonPlan.skillTargets.join(', ')}
- Content Themes: ${lessonPlan.contentThemes.join(', ')}
- Vocabulary Focus: ${lessonPlan.vocabularyFocus.join(', ')}
- Grammar Focus: ${lessonPlan.grammarFocus.join(', ')}
- Cultural Elements: ${lessonPlan.culturalElements.join(', ')}
- Practice Activities: ${lessonPlan.practiceActivities.join(', ')}
- Assessment Methods: ${lessonPlan.assessmentMethods.join(', ')}

**Module Context:**
- Module: ${moduleContext.moduleTitle}
- Description: ${moduleContext.moduleDescription}
- Vocabulary Themes: ${moduleContext.contentContext.vocabularyThemes.join(', ')}
- Grammar Focus Areas: ${moduleContext.contentContext.grammarFocusAreas.join(', ')}
- Cultural Elements: ${moduleContext.contentContext.culturalElements.join(', ')}

**User Progress & Adaptation:**
- Completed Lessons: ${userProgress.completedLessons.length}
- Learning Velocity: ${userProgress.learningVelocity} lessons/day
- Difficulty Preference: ${userProgress.difficultyPreference}
- Struggling Areas: ${userProgress.strugglingAreas.join(', ')}
- Strong Areas: ${userProgress.strongAreas.join(', ')}
- Skill Mastery: ${JSON.stringify(userProgress.skillMastery)}${previousContext}

**CRITICAL: Respond with ONLY valid JSON following FullLessonContentSchema**

Generate complete lesson content that:
1. Implements ALL elements from the lesson plan
2. Adapts difficulty to user's preference and performance
3. Provides extra support for struggling areas
4. Includes comprehensive exercises (8-12 exercises)
5. Incorporates vocabulary and grammar from the plan
6. Adds cultural context as specified
7. Creates engaging practice activities
8. Provides clear assessment criteria
9. Builds on previous lessons appropriately
10. Maintains pedagogical consistency with the module

Focus on creating rich, interactive content that brings the lesson plan to life!`;
  }

  private generateFallbackContent(lessonPlan: LessonPlan, moduleContext: ModuleContext): FullLessonContent {
    return {
      lessonId: lessonPlan.id,
      title: lessonPlan.title,
      type: lessonPlan.type,
      duration: lessonPlan.duration,
      content: {
        introduction: `Welcome to ${lessonPlan.title}. This lesson focuses on ${lessonPlan.contentThemes.join(', ')}.`,
        mainContent: {
          explanation: `In this lesson, we'll explore ${lessonPlan.learningObjectives.join(', ')}.`,
          examples: [
            {
              japanese: "こんにちは",
              romaji: "konnichiwa",
              english: "Hello",
              context: "Basic greeting"
            }
          ],
          keyPoints: lessonPlan.learningObjectives
        },
        exercises: [
          {
            id: "exercise-1",
            type: "multiple-choice",
            question: "Complete this lesson to continue your learning journey.",
            options: ["Continue", "Review", "Practice"],
            correctAnswer: "Continue",
            explanation: "This is a fallback exercise.",
            difficulty: lessonPlan.difficultyLevel
          }
        ],
        practiceActivities: lessonPlan.practiceActivities.map((activity, index) => ({
          name: `Practice Activity ${index + 1}`,
          description: activity,
          instructions: ["Follow the lesson plan", "Practice regularly"],
          timeEstimate: "10 minutes"
        }))
      },
      assessment: {
        completionCriteria: ["Complete all exercises", "Review key concepts"],
        masteryIndicators: lessonPlan.learningObjectives,
        nextSteps: ["Move to next lesson", "Review if needed"]
      },
      resources: {
        additionalReading: [],
        audioFiles: [],
        videoLinks: [],
        practiceLinks: []
      }
    };
  }
}

export const onDemandLessonService = new OnDemandLessonService();
