# AI Generation Debug System

This guide explains how to use the comprehensive debugging system for AI generation in the Japanese Learning App.

## 🚀 Quick Start

### Enable Debug Mode
1. **URL Parameter**: Add `?ai-debug` to any URL
2. **Keyboard Shortcut**: Press `Ctrl/Cmd + Shift + L`
3. **Console**: Run `aiLogger.setDebugMode(true)` in browser console

### Open Debug Panel
1. **Keyboard Shortcut**: Press `Ctrl/Cmd + Shift + D`
2. **Click the Bug Icon**: Bottom-right corner when debug mode is enabled
3. **URL Parameter**: Add `?ai-debug` to auto-open panel

## 🔧 Features

### Debug Panel Tabs

#### 📋 Logs Tab
- **Real-time logging** of all AI operations
- **Color-coded log levels**: Debug (gray), Info (blue), Warn (yellow), Error (red)
- **Detailed context** for each log entry
- **Expandable data** sections for request/response details

#### 🔄 Requests Tab
- **Complete AI request tracking** from start to finish
- **Performance metrics** (response times, token usage)
- **Success/failure indicators**
- **Fallback usage tracking**

#### 📊 Stats Tab
- **Performance overview**: Total requests, average response time
- **Success rates**: Success, error, and fallback percentages
- **System status**: Debug mode, auto-refresh, log counts

### Logging Levels

```typescript
// Debug: Detailed information for troubleshooting
aiLogger.debug('Service', 'operation', 'Detailed info', { data });

// Info: General information about operations
aiLogger.info('Service', 'operation', 'Operation started', { context });

// Warn: Warning conditions that don't stop execution
aiLogger.warn('Service', 'operation', 'API key missing', { fallback: true });

// Error: Error conditions that may cause failures
aiLogger.error('Service', 'operation', 'Request failed', { error });
```

## 🎯 What Gets Logged

### AI Service Operations
- **Roadmap Generation**: Full request/response cycle
- **Prompt Creation**: Generated prompts (in debug mode)
- **Response Parsing**: JSON extraction and validation
- **Error Handling**: Detailed error context and fallback usage

### Dynamic Module Generator
- **Module Creation**: Context generation and AI calls
- **Lesson Integration**: Lesson generation within modules
- **Performance Tracking**: Request timing and success rates

### Dynamic Lesson Generator
- **Lesson Generation**: Individual lesson creation
- **Context Building**: Module context and prompt construction
- **Validation**: Response parsing and structure validation

### Integration Bridge Service
- **Enhanced Roadmaps**: Integration metadata and consistency checks
- **Module Enhancement**: Contextual improvements and skill progression
- **Data Consistency**: Validation and integrity checks

## 🛠 Debug Panel Controls

### Header Controls
- **👁️ Debug Mode Toggle**: Enable/disable detailed logging
- **🕒 Auto-refresh Toggle**: Automatic log updates every second
- **📥 Export Logs**: Download logs as JSON file
- **🗑️ Clear Logs**: Remove all current logs
- **❌ Close Panel**: Hide the debug panel

### Keyboard Shortcuts
- `Ctrl/Cmd + Shift + D`: Toggle debug panel
- `Ctrl/Cmd + Shift + L`: Toggle debug mode

## 📊 Understanding the Data

### Request Tracking
Each AI request is tracked with:
- **Request ID**: Unique identifier for correlation
- **Service & Operation**: Which service made the request
- **Timing**: Start time, end time, duration
- **Model Parameters**: Model name, temperature, max tokens
- **Content**: Prompt and response lengths
- **Status**: Success, error, or fallback used

### Performance Metrics
- **Average Response Time**: Mean time for AI API calls
- **Success Rate**: Percentage of successful requests
- **Error Rate**: Percentage of failed requests
- **Fallback Rate**: Percentage of requests that used fallback content

### Log Data Structure
```typescript
{
  timestamp: "2024-01-15T10:30:00.000Z",
  level: LogLevel.INFO,
  service: "AIService",
  operation: "generateRoadmap",
  message: "Request completed successfully",
  data: {
    requestId: "ai_1705312200000_abc123",
    duration: "1.23s",
    responseLength: 15420
  }
}
```

## 🔍 Troubleshooting

### Common Issues

#### No Logs Appearing
- Check if debug mode is enabled
- Verify auto-refresh is on
- Try triggering an AI operation (generate roadmap)

#### Missing Request Details
- Enable debug mode for full prompt/response logging
- Check browser console for additional error messages

#### Performance Issues
- Large log files can slow down the panel
- Use "Clear Logs" button to reset
- Disable auto-refresh if not needed

### Debug Mode Benefits
When debug mode is enabled:
- **Full prompt logging**: See exact prompts sent to AI
- **Complete response logging**: View raw AI responses
- **Enhanced error context**: More detailed error information
- **Validation details**: Schema validation results

## 🚀 Advanced Usage

### Console Access
The logger is available globally in the browser console:
```javascript
// Access the logger
aiLogger

// Get all logs
aiLogger.getLogs()

// Get performance stats
aiLogger.getPerformanceStats()

// Export logs
aiLogger.exportLogs()

// Clear logs
aiLogger.clearLogs()

// Toggle debug mode
aiLogger.setDebugMode(true)
```

### Custom Logging
For developers adding new AI features:
```typescript
import { aiLogger } from '../utils/aiLogger';

// Start tracking a request
const requestId = aiLogger.startRequest('MyService', 'myOperation', {
  model: 'gpt-4',
  prompt: 'My prompt'
});

// Log progress
aiLogger.info('MyService', 'myOperation', 'Processing...', { requestId });

// End successfully
aiLogger.endRequest(requestId, response);

// Or end with error
aiLogger.endRequestWithError(requestId, error, fallbackUsed);
```

## 📈 Best Practices

1. **Enable debug mode during development** for full visibility
2. **Use appropriate log levels** (debug for detailed info, error for failures)
3. **Include relevant context** in log data objects
4. **Clear logs periodically** to maintain performance
5. **Export logs before clearing** if you need to analyze them later
6. **Use keyboard shortcuts** for quick access during development

## 🔒 Privacy & Security

- **Debug mode is disabled by default** in production
- **Sensitive data** (API keys) are never logged
- **Prompts and responses** are only logged in debug mode
- **Local storage only** - no data is sent to external servers
- **Clear logs** removes all debugging data

---

The AI debug system provides comprehensive visibility into the AI generation process, helping developers understand performance, troubleshoot issues, and optimize the user experience.
