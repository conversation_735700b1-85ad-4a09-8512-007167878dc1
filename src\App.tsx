import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import LandingPage from './pages/LandingPage'
import RoadmapGenerator from './pages/RoadmapGenerator'
import RoadmapDisplay from './pages/RoadmapDisplay'
import ModuleLessons from './pages/ModuleLessons'
import { AIDebugPanel } from './components/AIDebugPanel'
import { SplitAIDemo } from './components/SplitAIDemo'
import { useAIDebug } from './hooks/useAIDebug'
import { useState } from 'react'

function App() {
  const { isDebugPanelOpen, toggleDebugPanel } = useAIDebug();
  const [isSplitDemoOpen, setIsSplitDemoOpen] = useState(false);

  return (
    <Router>
      <div className="min-h-screen bg-gray-900">
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/generator" element={<RoadmapGenerator />} />
          <Route path="/roadmap" element={<RoadmapDisplay />} />
          <Route path="/module/:moduleId" element={<ModuleLessons />} />
        </Routes>

        {/* AI Debug Panel */}
        <AIDebugPanel
          isOpen={isDebugPanelOpen}
          onToggle={toggleDebugPanel}
        />

        {/* Split AI Demo */}
        {isSplitDemoOpen && (
          <SplitAIDemo onClose={() => setIsSplitDemoOpen(false)} />
        )}

        {/* Demo Access Button */}
        <button
          onClick={() => setIsSplitDemoOpen(true)}
          className="fixed bottom-4 right-20 bg-purple-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-purple-700 transition-colors z-40"
          title="Open Split AI Demo"
        >
          🚀 Split AI Demo
        </button>
      </div>
    </Router>
  )
}

export default App
