import { useState, useEffect } from 'react';
import { aiLogger } from '../utils/aiLogger';

/**
 * Hook for managing AI debug panel state and functionality
 */
export const useAIDebug = () => {
  const [isDebugPanelOpen, setIsDebugPanelOpen] = useState(false);
  const [debugMode, setDebugMode] = useState(false);

  useEffect(() => {
    // Initialize debug mode state
    setDebugMode(aiLogger.isDebugMode());

    // Check for debug mode activation via URL params or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const shouldOpenPanel = urlParams.has('ai-debug') || 
                           localStorage.getItem('ai-debug-panel-open') === 'true';
    
    if (shouldOpenPanel) {
      setIsDebugPanelOpen(true);
    }

    // Listen for keyboard shortcuts
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + D to toggle debug panel
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        toggleDebugPanel();
      }
      
      // Ctrl/Cmd + Shift + L to toggle debug mode
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'L') {
        event.preventDefault();
        toggleDebugMode();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const toggleDebugPanel = () => {
    const newState = !isDebugPanelOpen;
    setIsDebugPanelOpen(newState);
    localStorage.setItem('ai-debug-panel-open', newState.toString());
    
    if (newState) {
      aiLogger.info('DebugPanel', 'toggle', 'Debug panel opened');
    } else {
      aiLogger.info('DebugPanel', 'toggle', 'Debug panel closed');
    }
  };

  const toggleDebugMode = () => {
    const newMode = !debugMode;
    setDebugMode(newMode);
    aiLogger.setDebugMode(newMode);
    
    if (newMode) {
      console.log('🐛 AI Debug Mode Enabled');
      console.log('Keyboard shortcuts:');
      console.log('  Ctrl/Cmd + Shift + D: Toggle debug panel');
      console.log('  Ctrl/Cmd + Shift + L: Toggle debug mode');
      console.log('Available in console: aiLogger');
    } else {
      console.log('🐛 AI Debug Mode Disabled');
    }
  };

  const openDebugPanel = () => {
    if (!isDebugPanelOpen) {
      toggleDebugPanel();
    }
  };

  const closeDebugPanel = () => {
    if (isDebugPanelOpen) {
      toggleDebugPanel();
    }
  };

  return {
    isDebugPanelOpen,
    debugMode,
    toggleDebugPanel,
    toggleDebugMode,
    openDebugPanel,
    closeDebugPanel,
    
    // Convenience methods
    enableDebugMode: () => {
      if (!debugMode) toggleDebugMode();
    },
    disableDebugMode: () => {
      if (debugMode) toggleDebugMode();
    },
    
    // Logger access
    logger: aiLogger
  };
};
