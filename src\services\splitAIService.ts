/**
 * Split AI Service - Separates roadmap generation from lesson content generation
 * 
 * Phase 1: Generate roadmap with lesson PLANS only (fast)
 * Phase 2: Generate full lesson CONTENT on-demand (when user accesses lesson)
 */

import { z } from 'zod';
import { aiLogger } from '../utils/aiLogger';
import type { UserPreferences } from './aiService';

// Schema for lesson PLANS (not full content)
const LessonPlanSchema = z.object({
  id: z.string(),
  title: z.string(),
  type: z.enum(['reading', 'practice', 'quiz', 'video', 'conversation']),
  duration: z.string(),
  learningObjectives: z.array(z.string()),
  skillTargets: z.array(z.string()),
  contentThemes: z.array(z.string()),
  vocabularyFocus: z.array(z.string()),
  grammarFocus: z.array(z.string()),
  culturalElements: z.array(z.string()),
  practiceActivities: z.array(z.string()),
  assessmentMethods: z.array(z.string()),
  prerequisites: z.array(z.string()),
  difficultyLevel: z.number().min(1).max(5),
  estimatedDuration: z.string()
});

const ModuleOutlineSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  duration: z.string(),
  lessons: z.number(),
  dependsOn: z.array(z.string()).optional(),
  learningObjectives: z.array(z.string()),
  skillProgression: z.array(z.object({
    skillId: z.string(),
    skillName: z.string(),
    category: z.enum(['reading', 'writing', 'speaking', 'listening', 'grammar', 'vocabulary']),
    level: z.number().min(1).max(5),
    prerequisites: z.array(z.string()).optional(),
    dependentSkills: z.array(z.string()).optional(),
    masteryIndicators: z.array(z.string()),
    practiceActivities: z.array(z.string())
  })),
  // Only lesson PLANS - no full content
  lessonPlans: z.array(LessonPlanSchema),
  contentContext: z.object({
    vocabularyThemes: z.array(z.string()),
    grammarFocusAreas: z.array(z.string()),
    culturalElements: z.array(z.string()),
    practiceTypes: z.array(z.string())
  }),
  assessmentStrategy: z.object({
    formativeAssessments: z.array(z.string()),
    summativeAssessments: z.array(z.string()),
    progressIndicators: z.array(z.string())
  })
});

const RoadmapOutlineSchema = z.object({
  modules: z.array(ModuleOutlineSchema),
  estimatedCompletionTime: z.string(),
  personalizedTips: z.array(z.string()),
  overallSkillProgression: z.array(z.object({
    skillId: z.string(),
    skillName: z.string(),
    category: z.string(),
    level: z.number(),
    prerequisites: z.array(z.string()),
    dependentSkills: z.array(z.string()),
    masteryIndicators: z.array(z.string()),
    practiceActivities: z.array(z.string())
  })),
  learningPathway: z.object({
    milestones: z.array(z.string()),
    checkpoints: z.array(z.string()),
    adaptationPoints: z.array(z.string())
  }),
  integrationMetadata: z.object({
    generatedAt: z.string(),
    version: z.string(),
    contextHash: z.string()
  })
});

export type LessonPlan = z.infer<typeof LessonPlanSchema>;
export type ModuleOutline = z.infer<typeof ModuleOutlineSchema>;
export type RoadmapOutline = z.infer<typeof RoadmapOutlineSchema>;

// Schema for full lesson CONTENT (generated on-demand)
export interface LessonContent {
  introduction: string;
  mainContent: string;
  examples: string[];
  exercises: { question: string; answer: string; type?: string }[];
  vocabulary?: { term: string; reading: string; meaning: string }[];
  grammar?: { pattern: string; explanation: string; examples: string[] }[];
}

export interface FullLesson extends LessonPlan {
  content: LessonContent;
  completed: boolean;
  aiGenerated: boolean;
  generatedAt: Date;
}

class SplitAIService {
  private apiUrl = import.meta.env.VITE_BIGMODEL_API_URL || 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
  private apiKey = import.meta.env.VITE_BIGMODEL_API_KEY;

  /**
   * Phase 1: Generate roadmap with lesson PLANS only (fast - no full content)
   * This creates the complete structure and planning without generating actual lesson content
   */
  async generateRoadmapWithPlans(preferences: UserPreferences): Promise<RoadmapOutline> {
    const requestId = aiLogger.startRequest('SplitAIService', 'generateRoadmapWithPlans', {
      model: 'glm-4.5-x',
      temperature: 0.7,
      maxTokens: 64000 // High token limit for comprehensive plans
    });

    try {
      aiLogger.info('SplitAIService', 'generateRoadmapWithPlans', 'Generating roadmap with lesson plans only', {
        requestId,
        userLevel: preferences.currentLevel,
        learningGoal: preferences.learningGoal,
        focusAreas: preferences.focusAreas
      });

      if (!this.apiKey) {
        aiLogger.warn('SplitAIService', 'generateRoadmapWithPlans', 'API key not found, using fallback', { requestId });
        aiLogger.endRequestWithError(requestId, 'No API key configured', true);
        return this.generateFallbackRoadmapOutline(preferences);
      }

      const prompt = this.createRoadmapPlansPrompt(preferences);
      
      aiLogger.debug('SplitAIService', 'generateRoadmapWithPlans', 'Generated roadmap plans prompt', {
        requestId,
        promptLength: prompt.length,
        prompt: aiLogger.isDebugMode() ? prompt : '[Hidden - enable debug mode to see]'
      });

      aiLogger.info('SplitAIService', 'generateRoadmapWithPlans', 'Sending request to AI API', {
        requestId,
        endpoint: this.apiUrl,
        model: 'glm-4.5'
      });

      const requestBody = {
        model: 'glm-4.5-x',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 64000
      };

      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      aiLogger.debug('SplitAIService', 'generateRoadmapWithPlans', 'Received API response', {
        requestId,
        status: response.status,
        statusText: response.statusText
      });

      if (!response.ok) {
        const errorMessage = `API request failed: ${response.status} ${response.statusText}`;
        aiLogger.error('SplitAIService', 'generateRoadmapWithPlans', errorMessage, { requestId });
        throw new Error(errorMessage);
      }

      const data = await response.json();
      const aiContent = data.choices[0]?.message?.content;

      aiLogger.debug('SplitAIService', 'generateRoadmapWithPlans', 'Parsed API response', {
        requestId,
        hasContent: !!aiContent,
        contentLength: aiContent?.length || 0,
        usage: data.usage
      });

      if (!aiContent) {
        const error = 'No content received from AI';
        aiLogger.error('SplitAIService', 'generateRoadmapWithPlans', error, { requestId, responseData: data });
        throw new Error(error);
      }

      // Extract and parse JSON
      aiLogger.debug('SplitAIService', 'generateRoadmapWithPlans', 'Extracting JSON from AI response', { requestId });
      
      const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        aiLogger.error('SplitAIService', 'generateRoadmapWithPlans', 'No valid JSON found in AI response', {
          requestId,
          responsePreview: aiContent.substring(0, 500)
        });
        throw new Error('No valid JSON found in AI response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      
      aiLogger.debug('SplitAIService', 'generateRoadmapWithPlans', 'Validating parsed response with schema', {
        requestId,
        moduleCount: parsedResponse.modules?.length || 0
      });

      const validatedResponse = RoadmapOutlineSchema.parse(parsedResponse);

      // Add status and completion percentage to modules (like original service)
      const modulesWithStatus = validatedResponse.modules.map((module, index) => {
        const statusInfo = this.determineModuleStatus(index, preferences.currentLevel);
        return {
          ...module,
          status: statusInfo.status,
          completionPercentage: statusInfo.completionPercentage
        };
      });

      const finalResponse = {
        ...validatedResponse,
        modules: modulesWithStatus
      };

      aiLogger.endRequest(requestId, JSON.stringify(finalResponse), {
        moduleCount: modulesWithStatus.length,
        totalLessonPlans: modulesWithStatus.reduce((sum, mod) => sum + mod.lessonPlans.length, 0),
        estimatedTime: finalResponse.estimatedCompletionTime
      });

      aiLogger.info('SplitAIService', 'generateRoadmapWithPlans', 'Roadmap with plans generated successfully', {
        requestId,
        moduleCount: modulesWithStatus.length,
        totalLessonPlans: modulesWithStatus.reduce((sum, mod) => sum + mod.lessonPlans.length, 0),
        estimatedCompletionTime: finalResponse.estimatedCompletionTime
      });

      return finalResponse;

    } catch (error) {
      aiLogger.endRequestWithError(requestId, error as Error, false);
      aiLogger.error('SplitAIService', 'generateRoadmapWithPlans', 'Failed to generate roadmap with plans - using fallback', {
        requestId,
        error: error,
        fallbackUsed: true
      });
      
      const fallbackResponse = this.generateFallbackRoadmapOutline(preferences);
      aiLogger.info('SplitAIService', 'generateRoadmapWithPlans', 'Fallback roadmap generated', {
        requestId,
        moduleCount: fallbackResponse.modules.length
      });
      
      return fallbackResponse;
    }
  }

  /**
   * Phase 2: Generate full lesson CONTENT on-demand (when user accesses lesson)
   * This is called only when a user actually opens a specific lesson
   */
  async generateLessonContent(
    lessonPlan: LessonPlan,
    moduleContext: Pick<ModuleOutline, 'title' | 'description' | 'contentContext'>,
    userLevel: string,
    previousLessons?: LessonPlan[]
  ): Promise<FullLesson> {
    const requestId = aiLogger.startRequest('SplitAIService', 'generateLessonContent', {
      model: 'glm-4.5-x',
      temperature: 0.7,
      maxTokens: 64000,
      lessonId: lessonPlan.id
    });

    try {
      aiLogger.info('SplitAIService', 'generateLessonContent', 'Generating full lesson content on-demand', {
        requestId,
        lessonId: lessonPlan.id,
        lessonTitle: lessonPlan.title,
        lessonType: lessonPlan.type,
        moduleTitle: moduleContext.title
      });

      if (!this.apiKey) {
        aiLogger.warn('SplitAIService', 'generateLessonContent', 'API key not found, using fallback', { requestId });
        aiLogger.endRequestWithError(requestId, 'No API key configured', true);
        return this.generateFallbackLessonContent(lessonPlan, moduleContext, userLevel);
      }

      const prompt = this.createLessonContentPrompt(lessonPlan, moduleContext, userLevel, previousLessons);

      aiLogger.debug('SplitAIService', 'generateLessonContent', 'Generated lesson content prompt', {
        requestId,
        promptLength: prompt.length,
        lessonId: lessonPlan.id
      });

      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'glm-4.5-x',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 64000
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const aiContent = data.choices[0]?.message?.content;

      if (!aiContent) {
        throw new Error('No content received from AI');
      }

      // Parse the lesson content
      const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response');
      }

      const parsedContent = JSON.parse(jsonMatch[0]);
      
      const fullLesson: FullLesson = {
        ...lessonPlan,
        content: {
          introduction: parsedContent.introduction || '',
          mainContent: parsedContent.mainContent || '',
          examples: parsedContent.examples || [],
          exercises: parsedContent.exercises || [],
          vocabulary: parsedContent.vocabulary || [],
          grammar: parsedContent.grammar || []
        },
        completed: false,
        aiGenerated: true,
        generatedAt: new Date()
      };

      aiLogger.endRequest(requestId, JSON.stringify(fullLesson.content), {
        contentLength: JSON.stringify(fullLesson.content).length,
        exerciseCount: fullLesson.content.exercises.length,
        vocabularyCount: fullLesson.content.vocabulary?.length || 0,
        grammarCount: fullLesson.content.grammar?.length || 0
      });

      aiLogger.info('SplitAIService', 'generateLessonContent', 'Lesson content generated successfully', {
        requestId,
        lessonId: lessonPlan.id,
        exerciseCount: fullLesson.content.exercises.length,
        vocabularyCount: fullLesson.content.vocabulary?.length || 0
      });

      return fullLesson;

    } catch (error) {
      aiLogger.endRequestWithError(requestId, error as Error, true);
      aiLogger.error('SplitAIService', 'generateLessonContent', 'Failed to generate lesson content - using fallback', {
        requestId,
        lessonId: lessonPlan.id,
        error: error,
        fallbackUsed: true
      });
      
      const fallbackLesson = this.generateFallbackLessonContent(lessonPlan, moduleContext, userLevel);
      aiLogger.info('SplitAIService', 'generateLessonContent', 'Fallback lesson content generated', {
        requestId,
        lessonId: lessonPlan.id
      });
      
      return fallbackLesson;
    }
  }

  private createRoadmapPlansPrompt(preferences: UserPreferences): string {
    const userContext = this.analyzeUserContext(preferences);
    const contextualPrompt = this.generateContextualPrompt(userContext);
    
    return `You are an expert Japanese language learning curriculum designer with deep understanding of personalized learning paths and detailed lesson planning. Create a comprehensive learning roadmap with detailed lesson PLANS (not full content) based on the user analysis below:

**Detailed User Profile:**
${this.formatUserProfile(preferences, userContext)}

**Contextual Learning Requirements:**
${contextualPrompt}

**IMPORTANT: Generate lesson PLANS only - detailed outlines of what each lesson will cover, but NOT the actual lesson content (exercises, vocabulary lists, etc.). The full content will be generated later when the user accesses each lesson.**

**Enhanced Roadmap Generation Guidelines:**
1. Create detailed lesson PLANS for each module with specific learning objectives
2. Map skill progression dependencies between lessons and modules  
3. Define vocabulary themes and grammar focus areas for each lesson PLAN
4. Include cultural elements and practice activities in the PLAN
5. Establish assessment strategies and progress indicators
6. Ensure seamless integration between modules for cohesive learning
7. Provide comprehensive context for future lesson content generation

**CRITICAL: You MUST respond with ONLY valid JSON. No explanations, no markdown, no extra text.**

**Required Enhanced JSON Format:**
{
  "modules": [
    {
      "id": "descriptive-kebab-case-id",
      "title": "Engaging Module Title",
      "description": "Detailed description explaining value and outcomes (2-3 sentences)",
      "duration": "X-Y weeks",
      "lessons": number,
      "dependsOn": ["prerequisite-module-ids"],
      "learningObjectives": ["specific measurable objectives"],
      "skillProgression": [
        {
          "skillId": "skill-identifier",
          "skillName": "Skill Name", 
          "category": "reading|writing|speaking|listening|grammar|vocabulary",
          "level": 1-5,
          "prerequisites": ["prerequisite-skill-ids"],
          "dependentSkills": ["dependent-skill-ids"],
          "masteryIndicators": ["indicators of mastery"],
          "practiceActivities": ["specific practice activities"]
        }
      ],
      "lessonPlans": [
        {
          "id": "lesson-identifier",
          "title": "Lesson Title",
          "type": "reading|practice|quiz|video|conversation",
          "duration": "X minutes",
          "learningObjectives": ["specific lesson objectives"],
          "skillTargets": ["skills targeted in this lesson"],
          "contentThemes": ["main content themes"],
          "vocabularyFocus": ["vocabulary items to learn"],
          "grammarFocus": ["grammar points to cover"],
          "culturalElements": ["cultural aspects to include"],
          "practiceActivities": ["specific practice exercises"],
          "assessmentMethods": ["how to assess learning"],
          "prerequisites": ["required prior knowledge"],
          "difficultyLevel": 1-5,
          "estimatedDuration": "X minutes"
        }
      ],
      "contentContext": {
        "vocabularyThemes": ["overarching vocabulary themes"],
        "grammarFocusAreas": ["main grammar areas"],
        "culturalElements": ["cultural context elements"],
        "practiceTypes": ["types of practice activities"]
      },
      "assessmentStrategy": {
        "formativeAssessments": ["ongoing assessment methods"],
        "summativeAssessments": ["end-of-module assessments"],
        "progressIndicators": ["indicators of progress"]
      }
    }
  ],
  "estimatedCompletionTime": "X-Y months (based on ${preferences.timeCommitment} daily)",
  "personalizedTips": ["contextual tip based on profile", "goal-specific advice", "learning style optimization"],
  "overallSkillProgression": [
    {
      "skillId": "overall-skill-id",
      "skillName": "Overall Skill Name",
      "category": "skill category",
      "level": 1-5,
      "prerequisites": [],
      "dependentSkills": [],
      "masteryIndicators": [],
      "practiceActivities": []
    }
  ],
  "learningPathway": {
    "milestones": ["major learning milestones"],
    "checkpoints": ["progress checkpoints"],
    "adaptationPoints": ["points where learning can be adapted"]
  },
  "integrationMetadata": {
    "generatedAt": "${new Date().toISOString()}",
    "version": "2.0.0",
    "contextHash": "${this.generateContextHash(preferences)}"
  }
}

**Requirements:**
- Generate 4-7 modules with 15-45 lesson PLANS each
- Create detailed lesson PLANS for each lesson (not full content)
- Map skill dependencies across the entire roadmap
- Include comprehensive context for future lesson content generation
- Ensure proper JSON syntax with no trailing commas
- Use double quotes only
- Tailor everything to the user's unique learning profile

**RESPOND WITH ONLY THE JSON OBJECT:**`;
  }

  private createLessonContentPrompt(
    lessonPlan: LessonPlan,
    moduleContext: Pick<ModuleOutline, 'title' | 'description' | 'contentContext'>,
    userLevel: string,
    previousLessons?: LessonPlan[]
  ): string {
    const previousContext = previousLessons && previousLessons.length > 0 
      ? `\n**Previous Lessons Context:**\n${previousLessons.map(l => `- ${l.title}: ${l.learningObjectives.join(', ')}`).join('\n')}\n`
      : '';

    return `Generate FULL CONTENT for this specific Japanese lesson based on the detailed lesson plan:

**Lesson Plan:**
- ID: ${lessonPlan.id}
- Title: ${lessonPlan.title}
- Type: ${lessonPlan.type}
- Duration: ${lessonPlan.duration}
- Learning Objectives: ${lessonPlan.learningObjectives.join(', ')}
- Skill Targets: ${lessonPlan.skillTargets.join(', ')}
- Content Themes: ${lessonPlan.contentThemes.join(', ')}
- Vocabulary Focus: ${lessonPlan.vocabularyFocus.join(', ')}
- Grammar Focus: ${lessonPlan.grammarFocus.join(', ')}
- Cultural Elements: ${lessonPlan.culturalElements.join(', ')}
- Practice Activities: ${lessonPlan.practiceActivities.join(', ')}
- Assessment Methods: ${lessonPlan.assessmentMethods.join(', ')}
- Difficulty Level: ${lessonPlan.difficultyLevel}/5

**Module Context:**
- Module: ${moduleContext.title}
- Description: ${moduleContext.description}
- Vocabulary Themes: ${moduleContext.contentContext.vocabularyThemes.join(', ')}
- Grammar Focus Areas: ${moduleContext.contentContext.grammarFocusAreas.join(', ')}
- Cultural Elements: ${moduleContext.contentContext.culturalElements.join(', ')}
- Practice Types: ${moduleContext.contentContext.practiceTypes.join(', ')}
${previousContext}
**User Level:** ${userLevel}

**Generate COMPLETE lesson content following the lesson plan exactly:**

{
  "introduction": "Engaging lesson introduction that connects to the learning objectives",
  "mainContent": "Detailed lesson content with clear explanations, following the content themes",
  "examples": ["practical example 1", "practical example 2", "practical example 3"],
  "exercises": [
    {
      "question": "Exercise question based on practice activities",
      "answer": "Correct answer",
      "type": "multiple-choice|fill-blank|translation|matching"
    }
  ],
  "vocabulary": [
    {
      "term": "Japanese term from vocabulary focus",
      "reading": "hiragana/katakana reading",
      "meaning": "English meaning"
    }
  ],
  "grammar": [
    {
      "pattern": "Grammar pattern from grammar focus",
      "explanation": "Clear explanation appropriate for user level",
      "examples": ["example sentence 1", "example sentence 2"]
    }
  ]
}

**CRITICAL: Respond with ONLY the JSON object. No explanations, no markdown, no extra text.**`;
  }

  // Helper methods from original service
  private analyzeUserContext(preferences: UserPreferences): any {
    return {
      proficiencyLevel: this.assessProficiencyLevel(preferences),
      learningVelocity: this.estimateLearningVelocity(preferences),
      motivationFactors: this.identifyMotivationFactors(preferences),
      challengeAreas: this.identifyChallengingAreas(preferences),
      strengths: this.identifyStrengths(preferences)
    };
  }

  private generateContextualPrompt(userContext: any): string {
    let prompt = "";
    
    if (userContext.proficiencyLevel === 'absolute-beginner') {
      prompt += "- Focus on foundational elements with extensive practice and repetition\n";
      prompt += "- Include confidence-building exercises and clear progress markers\n";
    }
    
    if (userContext.learningVelocity === 'accelerated') {
      prompt += "- Provide challenging content with faster progression\n";
      prompt += "- Include advanced exercises and optional enrichment materials\n";
    }
    
    if (userContext.motivationFactors.includes('practical-application')) {
      prompt += "- Emphasize real-world usage and practical scenarios\n";
      prompt += "- Include situational dialogues and cultural context\n";
    }
    
    return prompt;
  }

  private formatUserProfile(preferences: UserPreferences, userContext: any): string {
    return `Current Level: ${preferences.currentLevel}
Learning Goal: ${preferences.learningGoal}
Time Commitment: ${preferences.timeCommitment}
Focus Areas: ${preferences.focusAreas.join(', ')}
Learning Style: ${preferences.learningStyle}
Proficiency Assessment: ${userContext.proficiencyLevel}
Learning Velocity: ${userContext.learningVelocity}
Motivation Factors: ${userContext.motivationFactors.join(', ')}
Challenge Areas: ${userContext.challengeAreas.join(', ')}
Strengths: ${userContext.strengths.join(', ')}`;
  }

  private assessProficiencyLevel(preferences: UserPreferences): string {
    return preferences.currentLevel;
  }

  private estimateLearningVelocity(preferences: UserPreferences): string {
    if (preferences.timeCommitment.includes('2+ hours')) return 'accelerated';
    if (preferences.timeCommitment.includes('1-2 hours')) return 'standard';
    return 'gradual';
  }

  private identifyMotivationFactors(preferences: UserPreferences): string[] {
    const factors = [];
    if (preferences.learningGoal.includes('travel')) factors.push('practical-application');
    if (preferences.learningGoal.includes('business')) factors.push('professional-development');
    if (preferences.learningGoal.includes('culture')) factors.push('cultural-interest');
    return factors.length > 0 ? factors : ['general-interest'];
  }

  private identifyChallengingAreas(preferences: UserPreferences): string[] {
    const challenges = [];
    if (preferences.currentLevel === 'absolute-beginner') challenges.push('writing-system');
    if (preferences.focusAreas.includes('speaking')) challenges.push('pronunciation');
    if (preferences.focusAreas.includes('listening')) challenges.push('comprehension');
    return challenges.length > 0 ? challenges : ['grammar'];
  }

  private identifyStrengths(preferences: UserPreferences): string[] {
    return preferences.focusAreas.length > 0 ? preferences.focusAreas : ['motivation'];
  }

  private generateContextHash(preferences: UserPreferences): string {
    const contextString = JSON.stringify(preferences);
    return btoa(contextString).substring(0, 16);
  }

  private determineModuleStatus(index: number, currentLevel: string): { status: string; completionPercentage: number } {
    if (index === 0) {
      return { status: 'current', completionPercentage: 0 };
    } else if (index <= 2 && currentLevel !== 'absolute-beginner') {
      return { status: 'available', completionPercentage: 0 };
    } else {
      return { status: 'locked', completionPercentage: 0 };
    }
  }

  private generateFallbackRoadmapOutline(preferences: UserPreferences): RoadmapOutline {
    return {
      modules: [
        {
          id: 'hiragana-basics',
          title: 'Hiragana Fundamentals',
          description: 'Master the basic Japanese syllabary with systematic practice and cultural context.',
          duration: '2-3 weeks',
          lessons: 8,
          learningObjectives: ['Read all hiragana characters', 'Write hiragana characters', 'Understand stroke order'],
          skillProgression: [{
            skillId: 'hiragana-reading',
            skillName: 'Hiragana Reading',
            category: 'reading',
            level: 1,
            prerequisites: [],
            dependentSkills: ['katakana-reading'],
            masteryIndicators: ['Can read all 46 basic hiragana', 'Recognizes characters in context'],
            practiceActivities: ['Character recognition', 'Writing practice', 'Reading simple words']
          }],
          lessonPlans: [
            {
              id: 'hiragana-lesson-1',
              title: 'Vowels (あいうえお)',
              type: 'reading',
              duration: '20 minutes',
              learningObjectives: ['Learn the 5 vowel sounds', 'Practice vowel pronunciation'],
              skillTargets: ['hiragana-reading', 'pronunciation'],
              contentThemes: ['basic-sounds', 'pronunciation'],
              vocabularyFocus: ['あ', 'い', 'う', 'え', 'お'],
              grammarFocus: ['vowel sounds'],
              culturalElements: ['Japanese writing system history'],
              practiceActivities: ['Character recognition', 'Pronunciation practice'],
              assessmentMethods: ['Recognition quiz', 'Pronunciation check'],
              prerequisites: [],
              difficultyLevel: 1,
              estimatedDuration: '20 minutes'
            }
          ],
          contentContext: {
            vocabularyThemes: ['basic-characters', 'pronunciation'],
            grammarFocusAreas: ['phonetics', 'writing-system'],
            culturalElements: ['writing-history', 'calligraphy'],
            practiceTypes: ['recognition', 'writing', 'pronunciation']
          },
          assessmentStrategy: {
            formativeAssessments: ['Daily character practice', 'Recognition exercises'],
            summativeAssessments: ['Module completion test', 'Writing assessment'],
            progressIndicators: ['Characters mastered', 'Recognition speed', 'Writing accuracy']
          }
        }
      ],
      estimatedCompletionTime: '3-6 months',
      personalizedTips: ['Practice daily for best results', 'Use spaced repetition for character memorization'],
      overallSkillProgression: [{
        skillId: 'japanese-basics',
        skillName: 'Japanese Language Basics',
        category: 'reading',
        level: 1,
        prerequisites: [],
        dependentSkills: ['intermediate-reading'],
        masteryIndicators: ['Can read basic hiragana', 'Understands basic pronunciation'],
        practiceActivities: ['Daily character practice', 'Reading simple texts']
      }],
      learningPathway: {
        milestones: ['Complete hiragana mastery', 'Begin katakana', 'Start basic vocabulary'],
        checkpoints: ['Weekly progress assessment', 'Character recognition test'],
        adaptationPoints: ['Adjust pace based on progress', 'Add extra practice if needed']
      },
      integrationMetadata: {
        generatedAt: new Date().toISOString(),
        version: '2.0.0',
        contextHash: this.generateContextHash(preferences)
      }
    };
  }

  private generateFallbackLessonContent(
    lessonPlan: LessonPlan,
    moduleContext: Pick<ModuleOutline, 'title' | 'description' | 'contentContext'>,
    userLevel: string
  ): FullLesson {
    return {
      ...lessonPlan,
      content: {
        introduction: `Welcome to ${lessonPlan.title}. This lesson will help you ${lessonPlan.learningObjectives.join(' and ')}.`,
        mainContent: `In this lesson, we'll explore ${lessonPlan.contentThemes.join(', ')} as part of ${moduleContext.title}. ${moduleContext.description}`,
        examples: [
          `Example demonstrating ${lessonPlan.contentThemes[0] || 'the lesson topic'}`,
          `Practical application of ${lessonPlan.skillTargets[0] || 'the target skill'}`,
          `Cultural context for ${lessonPlan.culturalElements[0] || 'Japanese learning'}`
        ],
        exercises: [
          {
            question: `Practice exercise for ${lessonPlan.title}`,
            answer: 'Sample answer',
            type: 'fill-blank'
          },
          {
            question: `Application exercise for ${lessonPlan.skillTargets[0] || 'target skill'}`,
            answer: 'Sample answer',
            type: 'multiple-choice'
          }
        ],
        vocabulary: lessonPlan.vocabularyFocus.slice(0, 3).map((term, index) => ({
          term: term,
          reading: `reading${index + 1}`,
          meaning: `Meaning for ${term}`
        })),
        grammar: lessonPlan.grammarFocus.slice(0, 2).map(pattern => ({
          pattern: pattern,
          explanation: `Explanation of ${pattern} grammar pattern`,
          examples: [`Example 1 with ${pattern}`, `Example 2 with ${pattern}`]
        }))
      },
      completed: false,
      aiGenerated: false,
      generatedAt: new Date()
    };
  }
}

export const splitAIService = new SplitAIService();
