/**
 * Progressive Roadmap Display Component
 * 
 * Shows the current module and upcoming phases in a pedagogically-sound way.
 * Generates new modules progressively as the user advances.
 */

import React, { useState, useEffect } from 'react';
import { progressiveWorkflowService } from '../services/progressiveWorkflowService';
import type { ProgressiveRoadmap, LearningProgress } from '../services/progressiveWorkflowService';
import type { RoadmapOutline, DetailedModule } from '../services/progressiveRoadmapService';

interface ProgressiveRoadmapDisplayProps {
  userId: string;
  initialRoadmap?: ProgressiveRoadmap;
  onModuleGenerated?: (module: DetailedModule) => void;
}

const ProgressiveRoadmapDisplay: React.FC<ProgressiveRoadmapDisplayProps> = ({
  userId,
  initialRoadmap,
  onModuleGenerated
}) => {
  const [roadmap, setRoadmap] = useState<ProgressiveRoadmap | null>(initialRoadmap || null);
  const [isGeneratingNext, setIsGeneratingNext] = useState(false);
  const [userProgress, setUserProgress] = useState<LearningProgress | null>(null);

  useEffect(() => {
    if (!roadmap) {
      const existingRoadmap = progressiveWorkflowService.getProgressiveRoadmap(userId);
      if (existingRoadmap) {
        setRoadmap(existingRoadmap);
        setUserProgress(existingRoadmap.userProgress);
      }
    }
  }, [userId, roadmap]);

  const handleProgressUpdate = async (progressUpdate: Partial<LearningProgress>) => {
    if (!roadmap || !userProgress) return;

    const updatedProgress = { ...userProgress, ...progressUpdate };
    setUserProgress(updatedProgress);

    // Update progress in service
    await progressiveWorkflowService.updateUserProgress(userId, progressUpdate);

    // Check if next module should be generated
    try {
      setIsGeneratingNext(true);
      const nextModule = await progressiveWorkflowService.checkAndGenerateNextModule(userId, updatedProgress);
      
      if (nextModule) {
        // Update roadmap with new module
        const updatedRoadmap = progressiveWorkflowService.getProgressiveRoadmap(userId);
        if (updatedRoadmap) {
          setRoadmap(updatedRoadmap);
          onModuleGenerated?.(nextModule);
        }
      }
    } catch (error) {
      console.error('Failed to check/generate next module:', error);
    } finally {
      setIsGeneratingNext(false);
    }
  };

  const handleRequestNextModule = async () => {
    if (!roadmap || !userProgress || isGeneratingNext) return;

    try {
      setIsGeneratingNext(true);
      
      // Simulate progress to trigger next module generation
      const simulatedProgress = {
        ...userProgress,
        completionRate: 0.8, // Simulate 80% completion
        learningVelocity: userProgress.learningVelocity * 1.2 // Slight boost
      };

      const nextModule = await progressiveWorkflowService.checkAndGenerateNextModule(userId, simulatedProgress);
      
      if (nextModule) {
        const updatedRoadmap = progressiveWorkflowService.getProgressiveRoadmap(userId);
        if (updatedRoadmap) {
          setRoadmap(updatedRoadmap);
          setUserProgress(updatedRoadmap.userProgress);
          onModuleGenerated?.(nextModule);
        }
      }
    } catch (error) {
      console.error('Failed to generate next module:', error);
    } finally {
      setIsGeneratingNext(false);
    }
  };

  if (!roadmap) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Loading progressive roadmap...</p>
      </div>
    );
  }

  const currentModule = roadmap.generatedModules.get(roadmap.currentPhase);
  const outline = roadmap.outline;

  return (
    <div className="space-y-8">
      {/* Learning Path Overview */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-red-400 mb-4">Your Learning Journey</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-500">{outline.learningPath.totalEstimatedModules}</div>
            <div className="text-gray-400">Total Phases</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-500">{roadmap.generatedModules.size}</div>
            <div className="text-gray-400">Generated</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-500">{outline.learningPath.estimatedCompletionTime}</div>
            <div className="text-gray-400">Est. Time</div>
          </div>
        </div>
        
        {/* Progress Indicators */}
        {userProgress && (
          <div className="mt-6 space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Learning Velocity</span>
              <span className="text-yellow-400">{userProgress.learningVelocity.toFixed(1)} lessons/day</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Completion Rate</span>
              <span className="text-green-400">{(userProgress.completionRate * 100).toFixed(0)}%</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Difficulty Preference</span>
              <span className="text-blue-400 capitalize">{userProgress.difficultyPreference}</span>
            </div>
          </div>
        )}
      </div>

      {/* Current Phase */}
      {currentModule && (
        <div className="bg-gray-800 border border-green-600 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-green-400">Current Phase: {currentModule.title}</h3>
            <span className="px-3 py-1 bg-green-600 text-green-100 rounded-full text-sm">Active</span>
          </div>
          
          <p className="text-gray-300 mb-4">{currentModule.description}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <span className="text-gray-400">Duration:</span>
              <span className="ml-2 text-white">{currentModule.duration}</span>
            </div>
            <div>
              <span className="text-gray-400">Lessons:</span>
              <span className="ml-2 text-white">{currentModule.lessons}</span>
            </div>
          </div>

          {/* Learning Objectives */}
          <div className="mb-4">
            <h4 className="text-lg font-semibold text-yellow-400 mb-2">Learning Objectives</h4>
            <ul className="list-disc list-inside space-y-1">
              {currentModule.learningObjectives.map((objective, index) => (
                <li key={index} className="text-gray-300">{objective}</li>
              ))}
            </ul>
          </div>

          {/* Adaptive Elements */}
          {currentModule.adaptiveElements && (
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Personalized for You</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {currentModule.adaptiveElements.difficultyAdjustments.length > 0 && (
                  <div>
                    <span className="text-gray-400">Difficulty:</span>
                    <ul className="ml-4 text-purple-300">
                      {currentModule.adaptiveElements.difficultyAdjustments.map((adj, index) => (
                        <li key={index}>• {adj}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {currentModule.adaptiveElements.reinforcementAreas.length > 0 && (
                  <div>
                    <span className="text-gray-400">Extra Practice:</span>
                    <ul className="ml-4 text-orange-300">
                      {currentModule.adaptiveElements.reinforcementAreas.map((area, index) => (
                        <li key={index}>• {area}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Progress Update Buttons */}
          <div className="mt-6 flex gap-3">
            <button
              onClick={() => handleProgressUpdate({ completionRate: Math.min(1, (userProgress?.completionRate || 0) + 0.1) })}
              className="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded transition-colors"
            >
              Mark Progress
            </button>
            <button
              onClick={() => handleProgressUpdate({ learningVelocity: (userProgress?.learningVelocity || 1) * 1.1 })}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded transition-colors"
            >
              Increase Pace
            </button>
          </div>
        </div>
      )}

      {/* Upcoming Phases */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <h3 className="text-xl font-bold text-blue-400 mb-4">Upcoming Learning Phases</h3>
        
        <div className="space-y-4">
          {outline.upcomingPhases.map((phase, index) => {
            const isGenerated = roadmap.generatedModules.has(phase.phaseId);
            const isNext = phase.phaseId === roadmap.nextPhaseToGenerate;
            
            return (
              <div
                key={phase.phaseId}
                className={`border rounded-lg p-4 ${
                  isGenerated 
                    ? 'border-green-600 bg-green-900/20' 
                    : isNext 
                      ? 'border-yellow-600 bg-yellow-900/20' 
                      : 'border-gray-600 bg-gray-700/50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-lg font-semibold text-white">{phase.phaseName}</h4>
                  <div className="flex items-center gap-2">
                    {isGenerated && (
                      <span className="px-2 py-1 bg-green-600 text-green-100 rounded text-xs">Generated</span>
                    )}
                    {isNext && (
                      <span className="px-2 py-1 bg-yellow-600 text-yellow-100 rounded text-xs">Next</span>
                    )}
                    {!isGenerated && !isNext && (
                      <span className="px-2 py-1 bg-gray-600 text-gray-300 rounded text-xs">Locked</span>
                    )}
                  </div>
                </div>
                
                <p className="text-gray-300 mb-3">{phase.description}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Duration:</span>
                    <span className="ml-2 text-white">{phase.estimatedDuration}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Core Skills:</span>
                    <span className="ml-2 text-white">{phase.coreSkills.join(', ')}</span>
                  </div>
                </div>

                {/* Unlock Conditions */}
                {phase.unlockConditions.length > 0 && (
                  <div className="mt-3 p-3 bg-gray-600 rounded">
                    <span className="text-gray-400 text-sm">Unlock Requirements:</span>
                    <ul className="mt-1 text-sm text-gray-300">
                      {phase.unlockConditions.map((condition, condIndex) => (
                        <li key={condIndex}>• {condition}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Generate Next Module Button */}
        {roadmap.nextPhaseToGenerate && (
          <div className="mt-6 text-center">
            <button
              onClick={handleRequestNextModule}
              disabled={isGeneratingNext}
              className="px-6 py-3 bg-purple-600 hover:bg-purple-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-lg transition-colors"
            >
              {isGeneratingNext ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Generating Next Phase...
                </div>
              ) : (
                'Generate Next Phase'
              )}
            </button>
            <p className="text-gray-400 text-sm mt-2">
              Generate the next learning phase based on your current progress
            </p>
          </div>
        )}
      </div>

      {/* Personalized Tips */}
      {outline.personalizedTips.length > 0 && (
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-xl font-bold text-yellow-400 mb-4">Personalized Learning Tips</h3>
          <ul className="space-y-2">
            {outline.personalizedTips.map((tip, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-yellow-400 mt-1">💡</span>
                <span className="text-gray-300">{tip}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ProgressiveRoadmapDisplay;
