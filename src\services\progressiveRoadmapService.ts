/**
 * Progressive Roadmap Generation Service
 * 
 * Generates roadmaps gradually as users progress, maintaining pedagogical consistency
 * and adapting to actual learning patterns and performance data.
 */

import { z } from 'zod';
import { aiLogger } from '../utils/aiLogger';
import { splitAIService } from './splitAIService';
import type { UserPreferences } from './aiService';

// Schema for roadmap outline (high-level structure only)
const RoadmapOutlineSchema = z.object({
  learningPath: z.object({
    totalEstimatedModules: z.number().min(3).max(10),
    estimatedCompletionTime: z.string(),
    overallProgression: z.array(z.string()),
    majorMilestones: z.array(z.string()),
    adaptationPoints: z.array(z.string())
  }),
  currentPhase: z.object({
    phaseId: z.string(),
    phaseName: z.string(),
    description: z.string(),
    estimatedDuration: z.string(),
    coreSkills: z.array(z.string()),
    prerequisites: z.array(z.string()),
    nextPhasePreview: z.string()
  }),
  upcomingPhases: z.array(z.object({
    phaseId: z.string(),
    phaseName: z.string(),
    description: z.string(),
    estimatedDuration: z.string(),
    coreSkills: z.array(z.string()),
    prerequisites: z.array(z.string()),
    unlockConditions: z.array(z.string())
  })),
  personalizedTips: z.array(z.string()),
  metadata: z.object({
    generatedAt: z.string(),
    userLevel: z.string(),
    learningGoal: z.string(),
    adaptiveFactors: z.array(z.string())
  })
});

// Schema for detailed module generation (when user approaches a phase)
const DetailedModuleSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  duration: z.string(),
  lessons: z.number(),
  learningObjectives: z.array(z.string()),
  skillProgression: z.array(z.object({
    skillId: z.string(),
    skillName: z.string(),
    category: z.enum(['reading', 'writing', 'speaking', 'listening', 'grammar', 'vocabulary']),
    level: z.number().min(1).max(5),
    prerequisites: z.array(z.string()),
    masteryIndicators: z.array(z.string()),
    practiceActivities: z.array(z.string())
  })),
  lessonPlans: z.array(z.object({
    id: z.string(),
    title: z.string(),
    type: z.enum(['reading', 'practice', 'quiz', 'video', 'conversation']),
    duration: z.string(),
    learningObjectives: z.array(z.string()),
    skillTargets: z.array(z.string()),
    contentThemes: z.array(z.string()),
    vocabularyFocus: z.array(z.string()),
    grammarFocus: z.array(z.string()),
    culturalElements: z.array(z.string()),
    practiceActivities: z.array(z.string()),
    assessmentMethods: z.array(z.string()),
    prerequisites: z.array(z.string()),
    difficultyLevel: z.number().min(1).max(5),
    estimatedDuration: z.string()
  })),
  contentContext: z.object({
    vocabularyThemes: z.array(z.string()),
    grammarFocusAreas: z.array(z.string()),
    culturalElements: z.array(z.string()),
    practiceTypes: z.array(z.string())
  }),
  assessmentStrategy: z.object({
    formativeAssessments: z.array(z.string()),
    summativeAssessments: z.array(z.string()),
    progressIndicators: z.array(z.string()),
    masteryThreshold: z.number().min(0.7).max(1.0)
  }),
  adaptiveElements: z.object({
    difficultyAdjustments: z.array(z.string()),
    paceModifications: z.array(z.string()),
    reinforcementAreas: z.array(z.string()),
    extensionActivities: z.array(z.string())
  })
});

export type RoadmapOutline = z.infer<typeof RoadmapOutlineSchema>;
export type DetailedModule = z.infer<typeof DetailedModuleSchema>;

// Progress tracking for adaptive generation
export interface LearningProgress {
  completedModules: string[];
  currentModule: string | null;
  skillMastery: Record<string, number>; // skill -> mastery level (0-1)
  learningVelocity: number; // lessons per day
  difficultyPreference: 'easier' | 'standard' | 'challenging';
  strugglingAreas: string[];
  strongAreas: string[];
  timeSpentPerLesson: number; // average minutes
  completionRate: number; // percentage of lessons completed
  lastActiveDate: string;
}

class ProgressiveRoadmapService {
  private apiUrl = import.meta.env.VITE_BIGMODEL_API_URL || 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
  private apiKey = import.meta.env.VITE_BIGMODEL_API_KEY;

  /**
   * Phase 1: Generate high-level roadmap outline only
   * Shows learning path structure without detailed modules
   */
  async generateRoadmapOutline(preferences: UserPreferences): Promise<RoadmapOutline> {
    const requestId = aiLogger.startRequest('ProgressiveRoadmapService', 'generateRoadmapOutline', {
      model: 'glm-4.5-x',
      temperature: 0.7,
      maxTokens: 64000
    });

    try {
      aiLogger.info('ProgressiveRoadmapService', 'generateRoadmapOutline', 'Generating high-level roadmap outline', {
        requestId,
        userLevel: preferences.currentLevel,
        learningGoal: preferences.learningGoal
      });

      if (!this.apiKey) {
        aiLogger.warn('ProgressiveRoadmapService', 'generateRoadmapOutline', 'API key not found, using fallback', { requestId });
        return this.generateFallbackOutline(preferences);
      }

      const prompt = this.createOutlinePrompt(preferences);
      
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'glm-4.5-x',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 64000
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const aiContent = data.choices[0]?.message?.content;

      if (!aiContent) {
        throw new Error('No content received from AI');
      }

      const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      const validatedResponse = RoadmapOutlineSchema.parse(parsedResponse);

      aiLogger.endRequest(requestId, JSON.stringify(validatedResponse), {
        totalEstimatedModules: validatedResponse.learningPath.totalEstimatedModules,
        currentPhase: validatedResponse.currentPhase.phaseName
      });

      aiLogger.info('ProgressiveRoadmapService', 'generateRoadmapOutline', 'Roadmap outline generated successfully', {
        requestId,
        totalEstimatedModules: validatedResponse.learningPath.totalEstimatedModules,
        currentPhase: validatedResponse.currentPhase.phaseName
      });

      return validatedResponse;

    } catch (error) {
      aiLogger.endRequestWithError(requestId, error as Error, true);
      aiLogger.error('ProgressiveRoadmapService', 'generateRoadmapOutline', 'Failed to generate roadmap outline', {
        requestId,
        error: error
      });
      
      return this.generateFallbackOutline(preferences);
    }
  }

  /**
   * Phase 2: Generate detailed module when user approaches it
   * Uses progress data to inform pedagogically appropriate content
   */
  async generateDetailedModule(
    phaseId: string,
    userPreferences: UserPreferences,
    learningProgress: LearningProgress,
    roadmapOutline: RoadmapOutline
  ): Promise<DetailedModule> {
    const requestId = aiLogger.startRequest('ProgressiveRoadmapService', 'generateDetailedModule', {
      model: 'glm-4.5-x',
      temperature: 0.7,
      maxTokens: 64000,
      phaseId
    });

    try {
      aiLogger.info('ProgressiveRoadmapService', 'generateDetailedModule', 'Generating detailed module with progress adaptation', {
        requestId,
        phaseId,
        completedModules: learningProgress.completedModules.length,
        learningVelocity: learningProgress.learningVelocity,
        strugglingAreas: learningProgress.strugglingAreas
      });

      if (!this.apiKey) {
        aiLogger.warn('ProgressiveRoadmapService', 'generateDetailedModule', 'API key not found, using fallback', { requestId });
        return this.generateFallbackModule(phaseId, userPreferences, learningProgress);
      }

      const prompt = this.createDetailedModulePrompt(phaseId, userPreferences, learningProgress, roadmapOutline);
      
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'glm-4.5-x',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 64000
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const aiContent = data.choices[0]?.message?.content;

      if (!aiContent) {
        throw new Error('No content received from AI');
      }

      const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      const validatedResponse = DetailedModuleSchema.parse(parsedResponse);

      aiLogger.endRequest(requestId, JSON.stringify(validatedResponse), {
        moduleId: validatedResponse.id,
        lessonCount: validatedResponse.lessons,
        skillCount: validatedResponse.skillProgression.length
      });

      aiLogger.info('ProgressiveRoadmapService', 'generateDetailedModule', 'Detailed module generated successfully', {
        requestId,
        moduleId: validatedResponse.id,
        lessonCount: validatedResponse.lessons,
        adaptiveElements: validatedResponse.adaptiveElements
      });

      return validatedResponse;

    } catch (error) {
      aiLogger.endRequestWithError(requestId, error as Error, true);
      aiLogger.error('ProgressiveRoadmapService', 'generateDetailedModule', 'Failed to generate detailed module', {
        requestId,
        phaseId,
        error: error
      });
      
      return this.generateFallbackModule(phaseId, userPreferences, learningProgress);
    }
  }

  /**
   * Analyze learning progress to inform next module generation
   */
  analyzeLearningProgress(
    completedLessons: any[],
    timeSpent: Record<string, number>,
    assessmentResults: Record<string, number>
  ): LearningProgress {
    const completedModules = [...new Set(completedLessons.map(lesson => lesson.moduleId))];
    const totalLessons = completedLessons.length;
    const totalTime = Object.values(timeSpent).reduce((sum, time) => sum + time, 0);
    
    // Calculate skill mastery from assessment results
    const skillMastery: Record<string, number> = {};
    Object.entries(assessmentResults).forEach(([skill, score]) => {
      skillMastery[skill] = Math.max(0, Math.min(1, score));
    });

    // Identify struggling and strong areas
    const strugglingAreas = Object.entries(skillMastery)
      .filter(([_, mastery]) => mastery < 0.7)
      .map(([skill, _]) => skill);
    
    const strongAreas = Object.entries(skillMastery)
      .filter(([_, mastery]) => mastery > 0.85)
      .map(([skill, _]) => skill);

    // Calculate learning velocity (lessons per day)
    const daysSinceStart = 30; // This should be calculated from actual start date
    const learningVelocity = totalLessons / daysSinceStart;

    // Determine difficulty preference based on performance
    let difficultyPreference: 'easier' | 'standard' | 'challenging' = 'standard';
    const averageMastery = Object.values(skillMastery).reduce((sum, mastery) => sum + mastery, 0) / Object.values(skillMastery).length;
    
    if (averageMastery < 0.6) {
      difficultyPreference = 'easier';
    } else if (averageMastery > 0.9) {
      difficultyPreference = 'challenging';
    }

    return {
      completedModules,
      currentModule: completedModules[completedModules.length - 1] || null,
      skillMastery,
      learningVelocity,
      difficultyPreference,
      strugglingAreas,
      strongAreas,
      timeSpentPerLesson: totalTime / Math.max(1, totalLessons),
      completionRate: 0.85, // This should be calculated from actual completion data
      lastActiveDate: new Date().toISOString()
    };
  }

  private createOutlinePrompt(preferences: UserPreferences): string {
    return `You are an expert Japanese language learning curriculum designer. Create a HIGH-LEVEL roadmap outline that shows the learning journey structure without detailed modules.

**User Profile:**
- Current Level: ${preferences.currentLevel}
- Learning Goal: ${preferences.learningGoal}
- Time Commitment: ${preferences.timeCommitment}
- Focus Areas: ${preferences.focusAreas.join(', ')}
- Learning Style: ${preferences.learningStyle}

**IMPORTANT: Generate OUTLINE ONLY - high-level learning path structure**

Create a progressive learning path that:
1. Shows major learning phases (not detailed modules)
2. Indicates skill progression milestones
3. Provides adaptation points for personalization
4. Maintains pedagogical consistency

**CRITICAL: Respond with ONLY valid JSON:**

{
  "learningPath": {
    "totalEstimatedModules": 5-8,
    "estimatedCompletionTime": "X-Y months",
    "overallProgression": ["phase1", "phase2", "phase3"],
    "majorMilestones": ["milestone1", "milestone2"],
    "adaptationPoints": ["point1", "point2"]
  },
  "currentPhase": {
    "phaseId": "phase-1",
    "phaseName": "Foundation Phase",
    "description": "Build core fundamentals",
    "estimatedDuration": "X weeks",
    "coreSkills": ["skill1", "skill2"],
    "prerequisites": [],
    "nextPhasePreview": "What comes next"
  },
  "upcomingPhases": [
    {
      "phaseId": "phase-2",
      "phaseName": "Phase Name",
      "description": "Phase description",
      "estimatedDuration": "X weeks",
      "coreSkills": ["skill1"],
      "prerequisites": ["phase-1"],
      "unlockConditions": ["condition1"]
    }
  ],
  "personalizedTips": ["tip1", "tip2"],
  "metadata": {
    "generatedAt": "${new Date().toISOString()}",
    "userLevel": "${preferences.currentLevel}",
    "learningGoal": "${preferences.learningGoal}",
    "adaptiveFactors": ["factor1", "factor2"]
  }
}`;
  }

  private createDetailedModulePrompt(
    phaseId: string,
    preferences: UserPreferences,
    progress: LearningProgress,
    outline: RoadmapOutline
  ): string {
    const phase = outline.currentPhase.phaseId === phaseId 
      ? outline.currentPhase 
      : outline.upcomingPhases.find(p => p.phaseId === phaseId);

    if (!phase) {
      throw new Error(`Phase ${phaseId} not found in roadmap outline`);
    }

    return `Generate a detailed module for this specific learning phase, adapting to the user's actual progress:

**Phase Information:**
- Phase ID: ${phaseId}
- Phase Name: ${phase.phaseName}
- Description: ${phase.description}
- Core Skills: ${phase.coreSkills.join(', ')}
- Prerequisites: ${phase.prerequisites.join(', ')}

**User Progress Data:**
- Completed Modules: ${progress.completedModules.length}
- Learning Velocity: ${progress.learningVelocity} lessons/day
- Difficulty Preference: ${progress.difficultyPreference}
- Struggling Areas: ${progress.strugglingAreas.join(', ')}
- Strong Areas: ${progress.strongAreas.join(', ')}
- Average Time per Lesson: ${progress.timeSpentPerLesson} minutes
- Skill Mastery: ${JSON.stringify(progress.skillMastery)}

**Pedagogical Requirements:**
1. Build on demonstrated mastery from previous modules
2. Address struggling areas with additional support
3. Leverage strong areas for confidence building
4. Adjust difficulty based on preference and performance
5. Include adaptive elements for personalization

**CRITICAL: Respond with ONLY valid JSON following DetailedModuleSchema**

Generate a complete module with lesson plans that:
- Adapts to the user's actual progress and performance
- Provides appropriate challenge level
- Includes reinforcement for struggling areas
- Offers extension activities for strong areas
- Maintains pedagogical consistency with previous learning`;
  }

  private generateFallbackOutline(preferences: UserPreferences): RoadmapOutline {
    return {
      learningPath: {
        totalEstimatedModules: 6,
        estimatedCompletionTime: '6-8 months',
        overallProgression: ['foundation', 'building', 'application'],
        majorMilestones: ['Basic reading', 'Conversational ability', 'Media comprehension'],
        adaptationPoints: ['After foundation', 'Mid-journey assessment', 'Advanced preparation']
      },
      currentPhase: {
        phaseId: 'foundation-phase',
        phaseName: 'Foundation Building',
        description: 'Establish core Japanese language fundamentals including writing systems and basic grammar',
        estimatedDuration: '6-8 weeks',
        coreSkills: ['hiragana-reading', 'basic-grammar', 'essential-vocabulary'],
        prerequisites: [],
        nextPhasePreview: 'Building practical communication skills'
      },
      upcomingPhases: [
        {
          phaseId: 'communication-phase',
          phaseName: 'Communication Building',
          description: 'Develop practical communication skills for daily interactions',
          estimatedDuration: '8-10 weeks',
          coreSkills: ['conversation-basics', 'practical-grammar', 'everyday-vocabulary'],
          prerequisites: ['foundation-phase'],
          unlockConditions: ['Complete hiragana mastery', 'Basic grammar understanding']
        }
      ],
      personalizedTips: [
        'Focus on consistent daily practice',
        'Use spaced repetition for character memorization'
      ],
      metadata: {
        generatedAt: new Date().toISOString(),
        userLevel: preferences.currentLevel,
        learningGoal: preferences.learningGoal,
        adaptiveFactors: ['beginner-friendly', 'structured-progression']
      }
    };
  }

  private generateFallbackModule(
    phaseId: string,
    preferences: UserPreferences,
    progress: LearningProgress
  ): DetailedModule {
    return {
      id: phaseId,
      title: 'Foundation Module',
      description: 'Core fundamentals adapted to your learning progress',
      duration: '4-6 weeks',
      lessons: 12,
      learningObjectives: ['Master basic skills', 'Build confidence', 'Prepare for next phase'],
      skillProgression: [
        {
          skillId: 'basic-reading',
          skillName: 'Basic Reading',
          category: 'reading',
          level: 1,
          prerequisites: [],
          masteryIndicators: ['Can read basic text'],
          practiceActivities: ['Character practice', 'Simple reading']
        }
      ],
      lessonPlans: [
        {
          id: 'lesson-1',
          title: 'Getting Started',
          type: 'reading',
          duration: '20 minutes',
          learningObjectives: ['Introduction to basics'],
          skillTargets: ['basic-reading'],
          contentThemes: ['fundamentals'],
          vocabularyFocus: ['basic-words'],
          grammarFocus: ['basic-patterns'],
          culturalElements: ['introduction'],
          practiceActivities: ['recognition-practice'],
          assessmentMethods: ['simple-quiz'],
          prerequisites: [],
          difficultyLevel: progress.difficultyPreference === 'easier' ? 1 : 2,
          estimatedDuration: '20 minutes'
        }
      ],
      contentContext: {
        vocabularyThemes: ['basics'],
        grammarFocusAreas: ['fundamentals'],
        culturalElements: ['introduction'],
        practiceTypes: ['recognition', 'practice']
      },
      assessmentStrategy: {
        formativeAssessments: ['Daily practice'],
        summativeAssessments: ['Module completion test'],
        progressIndicators: ['Completion rate', 'Accuracy'],
        masteryThreshold: 0.8
      },
      adaptiveElements: {
        difficultyAdjustments: progress.difficultyPreference === 'easier' 
          ? ['Slower pace', 'More repetition'] 
          : ['Standard pace'],
        paceModifications: [`Adjusted for ${progress.learningVelocity} lessons/day`],
        reinforcementAreas: progress.strugglingAreas,
        extensionActivities: progress.strongAreas.length > 0 
          ? ['Advanced exercises'] 
          : []
      }
    };
  }
}

export const progressiveRoadmapService = new ProgressiveRoadmapService();
