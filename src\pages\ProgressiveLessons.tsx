import { useEffect, useState } from 'react'
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom'
import { ArrowLeft, Play, CheckCircle, Clock, Target, Sparkles } from 'lucide-react'
import { onDemandLessonService } from '../services/onDemandLessonService'
import type { FullLessonContent, LessonPlan, ModuleContext, UserProgress } from '../services/onDemandLessonService'

const ProgressiveLessons = () => {
  const { moduleId, lessonId } = useParams<{ moduleId: string; lessonId?: string }>()
  const navigate = useNavigate()
  
  const [lessonPlans, setLessonPlans] = useState<LessonPlan[]>([])
  const [currentLessonContent, setCurrentLessonContent] = useState<FullLessonContent | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [completedLessons, setCompletedLessons] = useState<string[]>([])
  const [currentLessonIndex, setCurrentLessonIndex] = useState(0)

  // Mock data - in real app, this would come from progressive roadmap
  const mockModuleContext: ModuleContext = {
    moduleId: moduleId || '',
    moduleTitle: 'Foundation Building',
    moduleDescription: 'Master the fundamentals of Japanese language',
    contentContext: {
      vocabularyThemes: ['greetings', 'numbers', 'family'],
      grammarFocusAreas: ['particles', 'verb-conjugation', 'sentence-structure'],
      culturalElements: ['bowing', 'honorifics', 'social-hierarchy'],
      practiceTypes: ['reading', 'writing', 'speaking', 'listening']
    }
  }

  const mockUserProgress: UserProgress = {
    completedLessons: completedLessons,
    skillMastery: { 'hiragana': 0.8, 'basic-grammar': 0.6 },
    strugglingAreas: ['grammar'],
    strongAreas: ['vocabulary'],
    learningVelocity: 1.2,
    difficultyPreference: 'standard'
  }

  const mockLessonPlans: LessonPlan[] = [
    {
      id: 'lesson-1',
      title: 'Introduction to Hiragana',
      type: 'reading',
      duration: '25 minutes',
      learningObjectives: ['Learn first 10 hiragana characters', 'Practice stroke order'],
      skillTargets: ['hiragana-reading', 'writing-basics'],
      contentThemes: ['writing-systems', 'basic-characters'],
      vocabularyFocus: ['あ', 'い', 'う', 'え', 'お'],
      grammarFocus: [],
      culturalElements: ['Japanese writing history'],
      practiceActivities: ['Character tracing', 'Recognition exercises'],
      assessmentMethods: ['Character quiz', 'Writing practice'],
      prerequisites: [],
      difficultyLevel: 1,
      estimatedDuration: '25 minutes'
    },
    {
      id: 'lesson-2',
      title: 'Basic Greetings',
      type: 'conversation',
      duration: '30 minutes',
      learningObjectives: ['Master common greetings', 'Understand formal vs informal'],
      skillTargets: ['speaking', 'cultural-awareness'],
      contentThemes: ['daily-interactions', 'politeness'],
      vocabularyFocus: ['こんにちは', 'おはよう', 'こんばんは'],
      grammarFocus: ['です/である'],
      culturalElements: ['Bowing customs', 'Time-based greetings'],
      practiceActivities: ['Role-play', 'Audio practice'],
      assessmentMethods: ['Speaking assessment', 'Cultural quiz'],
      prerequisites: ['lesson-1'],
      difficultyLevel: 2,
      estimatedDuration: '30 minutes'
    },
    {
      id: 'lesson-3',
      title: 'Numbers 1-10',
      type: 'practice',
      duration: '20 minutes',
      learningObjectives: ['Count from 1-10', 'Use numbers in context'],
      skillTargets: ['vocabulary', 'practical-usage'],
      contentThemes: ['numbers', 'counting'],
      vocabularyFocus: ['いち', 'に', 'さん', 'よん', 'ご'],
      grammarFocus: ['Number usage patterns'],
      culturalElements: ['Lucky and unlucky numbers'],
      practiceActivities: ['Counting games', 'Number recognition'],
      assessmentMethods: ['Number quiz', 'Practical exercises'],
      prerequisites: ['lesson-1'],
      difficultyLevel: 1,
      estimatedDuration: '20 minutes'
    }
  ]

  useEffect(() => {
    // In real app, load lesson plans from progressive roadmap
    setLessonPlans(mockLessonPlans)
    
    // Load completed lessons from localStorage
    const saved = localStorage.getItem(`completed-lessons-${moduleId}`)
    if (saved) {
      setCompletedLessons(JSON.parse(saved))
    }
  }, [moduleId])

  useEffect(() => {
    // Auto-generate first lesson if specified in URL
    if (lessonId && lessonPlans.length > 0) {
      const lessonIndex = lessonPlans.findIndex(plan => plan.id === lessonId)
      if (lessonIndex >= 0) {
        setCurrentLessonIndex(lessonIndex)
        generateLessonContent(lessonPlans[lessonIndex])
      }
    }
  }, [lessonId, lessonPlans])

  const generateLessonContent = async (lessonPlan: LessonPlan) => {
    setIsGenerating(true)
    setError(null)
    
    try {
      const previousLessons = lessonPlans.slice(0, currentLessonIndex)
      const content = await onDemandLessonService.generateLessonContent(
        lessonPlan,
        mockModuleContext,
        mockUserProgress,
        previousLessons
      )
      
      setCurrentLessonContent(content)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate lesson content')
    } finally {
      setIsGenerating(false)
    }
  }

  const markLessonComplete = (lessonId: string) => {
    const updated = [...completedLessons, lessonId]
    setCompletedLessons(updated)
    localStorage.setItem(`completed-lessons-${moduleId}`, JSON.stringify(updated))
  }

  const nextLesson = () => {
    if (currentLessonIndex < lessonPlans.length - 1) {
      const nextIndex = currentLessonIndex + 1
      setCurrentLessonIndex(nextIndex)
      navigate(`/modules/${moduleId}/lessons/${lessonPlans[nextIndex].id}`)
    }
  }

  if (lessonPlans.length === 0) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading lesson plans...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <div className="flex items-center gap-4">
            <Link 
              to="/roadmap" 
              className="text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-white">{mockModuleContext.moduleTitle}</h1>
              <p className="text-sm text-gray-400">{mockModuleContext.moduleDescription}</p>
            </div>
          </div>
          <div className="text-sm text-gray-400">
            Lesson {currentLessonIndex + 1} of {lessonPlans.length}
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Lesson List Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-white mb-4">Lessons</h2>
              <div className="space-y-3">
                {lessonPlans.map((plan, index) => (
                  <button
                    key={plan.id}
                    onClick={() => {
                      setCurrentLessonIndex(index)
                      navigate(`/modules/${moduleId}/lessons/${plan.id}`)
                    }}
                    className={`w-full text-left p-3 rounded-lg border transition-all ${
                      index === currentLessonIndex
                        ? 'border-red-500 bg-red-900/20 text-red-100'
                        : completedLessons.includes(plan.id)
                        ? 'border-green-500 bg-green-900/20 text-green-100'
                        : 'border-gray-600 hover:border-gray-500 bg-gray-700 text-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {completedLessons.includes(plan.id) ? (
                        <CheckCircle className="w-4 h-4 text-green-400" />
                      ) : index === currentLessonIndex ? (
                        <Play className="w-4 h-4 text-red-400" />
                      ) : (
                        <Clock className="w-4 h-4 text-gray-500" />
                      )}
                      <span className="font-medium text-sm">{plan.title}</span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {plan.type} • {plan.duration}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {!currentLessonContent && !isGenerating && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-8 text-center">
                <Target className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-white mb-2">
                  Ready to start: {lessonPlans[currentLessonIndex]?.title}
                </h2>
                <p className="text-gray-400 mb-6">
                  Click the button below to generate your personalized lesson content.
                </p>
                <button
                  onClick={() => generateLessonContent(lessonPlans[currentLessonIndex])}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto"
                >
                  <Sparkles className="w-5 h-5" />
                  Generate Lesson Content
                </button>
              </div>
            )}

            {isGenerating && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
                <h2 className="text-xl font-semibold text-white mb-2">Generating Lesson Content</h2>
                <p className="text-gray-400">Creating personalized content based on your progress...</p>
              </div>
            )}

            {error && (
              <div className="bg-red-900/20 border border-red-500 rounded-lg p-6 mb-6">
                <h3 className="text-red-400 font-semibold mb-2">Error</h3>
                <p className="text-red-300">{error}</p>
                <button
                  onClick={() => generateLessonContent(lessonPlans[currentLessonIndex])}
                  className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
                >
                  Try Again
                </button>
              </div>
            )}

            {currentLessonContent && (
              <div className="space-y-6">
                {/* Lesson Header */}
                <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                  <h1 className="text-2xl font-bold text-white mb-2">{currentLessonContent.title}</h1>
                  <div className="flex items-center gap-4 text-gray-400 text-sm">
                    <span className={`px-3 py-1 rounded text-sm ${
                      currentLessonContent.type === 'reading' ? 'bg-blue-600' :
                      currentLessonContent.type === 'practice' ? 'bg-green-600' :
                      currentLessonContent.type === 'quiz' ? 'bg-purple-600' :
                      currentLessonContent.type === 'conversation' ? 'bg-orange-600' :
                      'bg-gray-600'
                    }`}>
                      {currentLessonContent.type}
                    </span>
                    <span>{currentLessonContent.duration}</span>
                  </div>
                </div>

                {/* Lesson Content */}
                <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                  <h2 className="text-lg font-semibold text-white mb-4">Introduction</h2>
                  <p className="text-gray-300 mb-6">{currentLessonContent.content.introduction}</p>
                  
                  <h2 className="text-lg font-semibold text-white mb-4">Main Content</h2>
                  <p className="text-gray-300 mb-4">{currentLessonContent.content.mainContent.explanation}</p>
                  
                  {currentLessonContent.content.mainContent.examples.length > 0 && (
                    <div className="mb-6">
                      <h3 className="font-medium text-white mb-3">Examples</h3>
                      <div className="space-y-3">
                        {currentLessonContent.content.mainContent.examples.map((example, index) => (
                          <div key={index} className="bg-gray-700 rounded p-4">
                            <div className="text-xl text-white mb-1">{example.japanese}</div>
                            {example.romaji && (
                              <div className="text-gray-400 text-sm mb-1">{example.romaji}</div>
                            )}
                            <div className="text-gray-300">{example.english}</div>
                            {example.context && (
                              <div className="text-gray-500 text-sm mt-1">{example.context}</div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4">
                  <button
                    onClick={() => markLessonComplete(currentLessonContent.lessonId)}
                    disabled={completedLessons.includes(currentLessonContent.lessonId)}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2"
                  >
                    <CheckCircle className="w-5 h-5" />
                    {completedLessons.includes(currentLessonContent.lessonId) ? 'Completed' : 'Mark Complete'}
                  </button>
                  
                  {currentLessonIndex < lessonPlans.length - 1 && (
                    <button
                      onClick={nextLesson}
                      className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                    >
                      Next Lesson
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProgressiveLessons
