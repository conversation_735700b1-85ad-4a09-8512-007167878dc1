/**
 * AI Generation Debug Logger
 * Provides comprehensive logging for AI service operations
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface AILogEntry {
  timestamp: string;
  level: LogLevel;
  service: string;
  operation: string;
  message: string;
  data?: any;
  duration?: number;
  requestId?: string;
}

export interface AIRequestLog {
  requestId: string;
  service: string;
  operation: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  prompt?: string;
  promptLength?: number;
  response?: string;
  responseLength?: number;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  error?: string;
  fallbackUsed?: boolean;
}

class AILogger {
  private logs: AILogEntry[] = [];
  private requestLogs: Map<string, AIRequestLog> = new Map();
  private currentLogLevel: LogLevel = LogLevel.INFO;
  private maxLogEntries = 1000;
  private debugMode = false;

  constructor() {
    // Enable debug mode based on environment, environment variable, localStorage, or URL params
    this.debugMode = import.meta.env.DEV ||
                     import.meta.env.VITE_AI_DEBUG_MODE === 'true' ||
                     localStorage.getItem('ai-debug-mode') === 'true' ||
                     new URLSearchParams(window.location.search).has('ai-debug');

    if (this.debugMode) {
      this.currentLogLevel = LogLevel.DEBUG;
      console.log('🤖 AI Debug Mode Enabled');
      console.log('Keyboard shortcuts:');
      console.log('  Ctrl/Cmd + Shift + D: Toggle debug panel');
      console.log('  Ctrl/Cmd + Shift + L: Toggle debug mode');
      console.log('Available in console: aiLogger');
    }
  }

  /**
   * Generate a unique request ID for tracking
   */
  generateRequestId(): string {
    return `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Start tracking an AI request
   */
  startRequest(service: string, operation: string, options?: {
    prompt?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
  }): string {
    const requestId = this.generateRequestId();
    const requestLog: AIRequestLog = {
      requestId,
      service,
      operation,
      startTime: performance.now(),
      ...options,
      promptLength: options?.prompt?.length
    };

    this.requestLogs.set(requestId, requestLog);
    
    this.log(LogLevel.DEBUG, service, operation, 
      `🚀 Starting AI request`, { 
        requestId, 
        model: options?.model,
        promptLength: options?.prompt?.length,
        temperature: options?.temperature,
        maxTokens: options?.maxTokens
      });

    if (this.debugMode && options?.prompt) {
      this.log(LogLevel.DEBUG, service, operation, 
        `📝 Request prompt`, { requestId, prompt: options.prompt });
    }

    return requestId;
  }

  /**
   * End tracking an AI request with success
   */
  endRequest(requestId: string, response: string, additionalData?: any): void {
    const requestLog = this.requestLogs.get(requestId);
    if (!requestLog) return;

    const endTime = performance.now();
    const duration = endTime - requestLog.startTime;

    requestLog.endTime = endTime;
    requestLog.duration = duration;
    requestLog.response = response;
    requestLog.responseLength = response.length;

    this.log(LogLevel.INFO, requestLog.service, requestLog.operation,
      `✅ AI request completed`, {
        requestId,
        duration: `${duration.toFixed(2)}ms`,
        responseLength: response.length,
        ...additionalData
      });

    if (this.debugMode) {
      this.log(LogLevel.DEBUG, requestLog.service, requestLog.operation,
        `📄 Response content`, { requestId, response });
    }
  }

  /**
   * End tracking an AI request with error
   */
  endRequestWithError(requestId: string, error: Error | string, fallbackUsed = false): void {
    const requestLog = this.requestLogs.get(requestId);
    if (!requestLog) return;

    const endTime = performance.now();
    const duration = endTime - requestLog.startTime;

    requestLog.endTime = endTime;
    requestLog.duration = duration;
    requestLog.error = error.toString();
    requestLog.fallbackUsed = fallbackUsed;

    this.log(LogLevel.ERROR, requestLog.service, requestLog.operation,
      `❌ AI request failed`, {
        requestId,
        duration: `${duration.toFixed(2)}ms`,
        error: error.toString(),
        fallbackUsed
      });
  }

  /**
   * Log a message with specified level
   */
  log(level: LogLevel, service: string, operation: string, message: string, data?: any): void {
    if (level < this.currentLogLevel) return;

    const logEntry: AILogEntry = {
      timestamp: new Date().toISOString(),
      level,
      service,
      operation,
      message,
      data
    };

    this.logs.push(logEntry);

    // Keep logs within limit
    if (this.logs.length > this.maxLogEntries) {
      this.logs = this.logs.slice(-this.maxLogEntries);
    }

    // Console output with appropriate styling
    const levelEmoji = {
      [LogLevel.DEBUG]: '🔍',
      [LogLevel.INFO]: 'ℹ️',
      [LogLevel.WARN]: '⚠️',
      [LogLevel.ERROR]: '❌'
    };

    const levelName = LogLevel[level];
    const emoji = levelEmoji[level];
    const timestamp = new Date().toLocaleTimeString();

    const logMessage = `${emoji} [${timestamp}] [${service}:${operation}] ${message}`;

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(logMessage, data || '');
        break;
      case LogLevel.INFO:
        console.info(logMessage, data || '');
        break;
      case LogLevel.WARN:
        console.warn(logMessage, data || '');
        break;
      case LogLevel.ERROR:
        console.error(logMessage, data || '');
        break;
    }
  }

  /**
   * Convenience methods for different log levels
   */
  debug(service: string, operation: string, message: string, data?: any): void {
    this.log(LogLevel.DEBUG, service, operation, message, data);
  }

  info(service: string, operation: string, message: string, data?: any): void {
    this.log(LogLevel.INFO, service, operation, message, data);
  }

  warn(service: string, operation: string, message: string, data?: any): void {
    this.log(LogLevel.WARN, service, operation, message, data);
  }

  error(service: string, operation: string, message: string, data?: any): void {
    this.log(LogLevel.ERROR, service, operation, message, data);
  }

  /**
   * Get all logs
   */
  getLogs(): AILogEntry[] {
    return [...this.logs];
  }

  /**
   * Get request logs
   */
  getRequestLogs(): AIRequestLog[] {
    return Array.from(this.requestLogs.values());
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): {
    totalRequests: number;
    averageResponseTime: number;
    successRate: number;
    errorRate: number;
    fallbackRate: number;
  } {
    const requests = Array.from(this.requestLogs.values());
    const completedRequests = requests.filter(r => r.duration !== undefined);
    
    if (completedRequests.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        successRate: 0,
        errorRate: 0,
        fallbackRate: 0
      };
    }

    const totalRequests = completedRequests.length;
    const averageResponseTime = completedRequests.reduce((sum, r) => sum + (r.duration || 0), 0) / totalRequests;
    const errorCount = completedRequests.filter(r => r.error).length;
    const fallbackCount = completedRequests.filter(r => r.fallbackUsed).length;

    return {
      totalRequests,
      averageResponseTime: Math.round(averageResponseTime * 100) / 100,
      successRate: Math.round(((totalRequests - errorCount) / totalRequests) * 100),
      errorRate: Math.round((errorCount / totalRequests) * 100),
      fallbackRate: Math.round((fallbackCount / totalRequests) * 100)
    };
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logs = [];
    this.requestLogs.clear();
    this.info('Logger', 'clearLogs', 'All logs cleared');
  }

  /**
   * Export logs as JSON
   */
  exportLogs(): string {
    return JSON.stringify({
      logs: this.logs,
      requestLogs: Array.from(this.requestLogs.values()),
      stats: this.getPerformanceStats(),
      exportedAt: new Date().toISOString()
    }, null, 2);
  }

  /**
   * Enable/disable debug mode
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
    this.currentLogLevel = enabled ? LogLevel.DEBUG : LogLevel.INFO;
    localStorage.setItem('ai-debug-mode', enabled.toString());
    
    this.info('Logger', 'setDebugMode', 
      `Debug mode ${enabled ? 'enabled' : 'disabled'}`, { debugMode: enabled });
  }

  /**
   * Check if debug mode is enabled
   */
  isDebugMode(): boolean {
    return this.debugMode;
  }
}

// Export singleton instance
export const aiLogger = new AILogger();

// Global access for debugging in browser console
if (typeof window !== 'undefined') {
  (window as any).aiLogger = aiLogger;
}
