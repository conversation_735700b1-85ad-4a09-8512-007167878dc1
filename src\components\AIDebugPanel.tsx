import React, { useState, useEffect } from 'react';
import { aiLogger, type AILogEntry, type AIRequestLog } from '../utils/aiLogger';
import { Bug, Download, Trash2, Eye, EyeOff, Clock, AlertCircle, CheckCircle, Info } from 'lucide-react';

interface AIDebugPanelProps {
  isOpen: boolean;
  onToggle: () => void;
}

export const AIDebugPanel: React.FC<AIDebugPanelProps> = ({ isOpen, onToggle }) => {
  const [logs, setLogs] = useState<AILogEntry[]>([]);
  const [requestLogs, setRequestLogs] = useState<AIRequestLog[]>([]);
  const [stats, setStats] = useState<any>({});
  const [activeTab, setActiveTab] = useState<'logs' | 'requests' | 'stats'>('logs');
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    const refreshData = () => {
      setLogs(aiLogger.getLogs());
      setRequestLogs(aiLogger.getRequestLogs());
      setStats(aiLogger.getPerformanceStats());
    };

    refreshData();

    if (autoRefresh) {
      const interval = setInterval(refreshData, 1000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const handleExportLogs = () => {
    const data = aiLogger.exportLogs();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-debug-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleClearLogs = () => {
    aiLogger.clearLogs();
    setLogs([]);
    setRequestLogs([]);
    setStats({});
  };

  const toggleDebugMode = () => {
    aiLogger.setDebugMode(!aiLogger.isDebugMode());
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getLogLevelIcon = (level: number) => {
    switch (level) {
      case 0: return <Bug className="w-4 h-4 text-gray-400" />;
      case 1: return <Info className="w-4 h-4 text-blue-400" />;
      case 2: return <AlertCircle className="w-4 h-4 text-yellow-400" />;
      case 3: return <AlertCircle className="w-4 h-4 text-red-400" />;
      default: return <Info className="w-4 h-4 text-gray-400" />;
    }
  };

  const getLogLevelColor = (level: number) => {
    switch (level) {
      case 0: return 'text-gray-400';
      case 1: return 'text-blue-400';
      case 2: return 'text-yellow-400';
      case 3: return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg z-50 transition-colors"
        title="Open AI Debug Panel"
      >
        <Bug className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 h-96 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Bug className="w-5 h-5 text-blue-400" />
          <span className="font-semibold text-white">AI Debug Panel</span>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={toggleDebugMode}
            className={`p-1 rounded ${aiLogger.isDebugMode() ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'}`}
            title={`Debug mode: ${aiLogger.isDebugMode() ? 'ON' : 'OFF'}`}
          >
            {aiLogger.isDebugMode() ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </button>
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`p-1 rounded ${autoRefresh ? 'bg-blue-600 text-white' : 'bg-gray-600 text-gray-300'}`}
            title={`Auto-refresh: ${autoRefresh ? 'ON' : 'OFF'}`}
          >
            <Clock className="w-4 h-4" />
          </button>
          <button
            onClick={handleExportLogs}
            className="p-1 rounded bg-gray-600 hover:bg-gray-500 text-gray-300"
            title="Export logs"
          >
            <Download className="w-4 h-4" />
          </button>
          <button
            onClick={handleClearLogs}
            className="p-1 rounded bg-red-600 hover:bg-red-500 text-white"
            title="Clear logs"
          >
            <Trash2 className="w-4 h-4" />
          </button>
          <button
            onClick={onToggle}
            className="p-1 rounded bg-gray-600 hover:bg-gray-500 text-gray-300"
            title="Close panel"
          >
            ×
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-700">
        {[
          { id: 'logs', label: 'Logs', count: logs.length },
          { id: 'requests', label: 'Requests', count: requestLogs.length },
          { id: 'stats', label: 'Stats', count: null }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 px-3 py-2 text-sm font-medium ${
              activeTab === tab.id
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            {tab.label}
            {tab.count !== null && (
              <span className="ml-1 text-xs bg-gray-600 px-1 rounded">
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'logs' && (
          <div className="h-full overflow-y-auto p-2 space-y-1">
            {logs.slice(-50).reverse().map((log, index) => (
              <div key={index} className="text-xs bg-gray-900 p-2 rounded border border-gray-700">
                <div className="flex items-center gap-2 mb-1">
                  {getLogLevelIcon(log.level)}
                  <span className="text-gray-400">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                  <span className="text-blue-400">{log.service}</span>
                  <span className="text-gray-500">→</span>
                  <span className="text-green-400">{log.operation}</span>
                </div>
                <div className={`${getLogLevelColor(log.level)} mb-1`}>
                  {log.message}
                </div>
                {log.data && (
                  <details className="text-gray-500">
                    <summary className="cursor-pointer hover:text-gray-400">Data</summary>
                    <pre className="mt-1 text-xs overflow-x-auto">
                      {JSON.stringify(log.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
            {logs.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                No logs yet. AI operations will appear here.
              </div>
            )}
          </div>
        )}

        {activeTab === 'requests' && (
          <div className="h-full overflow-y-auto p-2 space-y-1">
            {requestLogs.slice(-20).reverse().map((request, index) => (
              <div key={index} className="text-xs bg-gray-900 p-2 rounded border border-gray-700">
                <div className="flex items-center gap-2 mb-1">
                  {request.error ? (
                    <AlertCircle className="w-4 h-4 text-red-400" />
                  ) : (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  )}
                  <span className="text-blue-400">{request.service}</span>
                  <span className="text-gray-500">→</span>
                  <span className="text-green-400">{request.operation}</span>
                  {request.duration && (
                    <span className="text-yellow-400 ml-auto">
                      {formatDuration(request.duration)}
                    </span>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-2 text-gray-400">
                  <div>Model: {request.model || 'N/A'}</div>
                  <div>Tokens: {request.maxTokens || 'N/A'}</div>
                  <div>Prompt: {request.promptLength || 0} chars</div>
                  <div>Response: {request.responseLength || 0} chars</div>
                </div>
                {request.error && (
                  <div className="text-red-400 mt-1">
                    Error: {request.error}
                  </div>
                )}
                {request.fallbackUsed && (
                  <div className="text-yellow-400 mt-1">
                    ⚠️ Fallback used
                  </div>
                )}
              </div>
            ))}
            {requestLogs.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                No requests yet. AI API calls will appear here.
              </div>
            )}
          </div>
        )}

        {activeTab === 'stats' && (
          <div className="h-full overflow-y-auto p-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-900 p-3 rounded">
                  <div className="text-2xl font-bold text-white">{stats.totalRequests || 0}</div>
                  <div className="text-sm text-gray-400">Total Requests</div>
                </div>
                <div className="bg-gray-900 p-3 rounded">
                  <div className="text-2xl font-bold text-white">
                    {stats.averageResponseTime ? formatDuration(stats.averageResponseTime) : 'N/A'}
                  </div>
                  <div className="text-sm text-gray-400">Avg Response Time</div>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-2">
                <div className="bg-green-900 p-2 rounded text-center">
                  <div className="text-lg font-bold text-green-400">{stats.successRate || 0}%</div>
                  <div className="text-xs text-green-300">Success</div>
                </div>
                <div className="bg-red-900 p-2 rounded text-center">
                  <div className="text-lg font-bold text-red-400">{stats.errorRate || 0}%</div>
                  <div className="text-xs text-red-300">Errors</div>
                </div>
                <div className="bg-yellow-900 p-2 rounded text-center">
                  <div className="text-lg font-bold text-yellow-400">{stats.fallbackRate || 0}%</div>
                  <div className="text-xs text-yellow-300">Fallbacks</div>
                </div>
              </div>

              <div className="text-xs text-gray-500 space-y-1">
                <div>Debug Mode: {aiLogger.isDebugMode() ? 'Enabled' : 'Disabled'}</div>
                <div>Auto Refresh: {autoRefresh ? 'On' : 'Off'}</div>
                <div>Log Entries: {logs.length}</div>
                <div>Request Logs: {requestLogs.length}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
