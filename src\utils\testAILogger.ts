/**
 * Test utility for AI Logger
 * Use this to verify the logging system is working correctly
 */

import { aiLogger } from './aiLogger';

export const testAILogger = () => {
  console.log('🧪 Testing AI Logger...');
  
  // Test basic logging
  aiLogger.debug('TestService', 'testOperation', 'Debug message test', { testData: 'debug' });
  aiLogger.info('TestService', 'testOperation', 'Info message test', { testData: 'info' });
  aiLogger.warn('TestService', 'testOperation', 'Warning message test', { testData: 'warning' });
  aiLogger.error('TestService', 'testOperation', 'Error message test', { testData: 'error' });
  
  // Test request tracking
  const requestId = aiLogger.startRequest('TestService', 'testAICall', {
    prompt: 'Test prompt for logging',
    model: 'test-model',
    temperature: 0.7,
    maxTokens: 1000
  });
  
  // Simulate some processing time
  setTimeout(() => {
    aiLogger.endRequest(requestId, 'Test response from AI', {
      additionalData: 'test completion'
    });
    
    // Test error scenario
    const errorRequestId = aiLogger.startRequest('TestService', 'testErrorCall', {
      prompt: 'Test error prompt',
      model: 'test-model'
    });
    
    setTimeout(() => {
      aiLogger.endRequestWithError(errorRequestId, new Error('Test error'), true);
      
      // Show results
      console.log('✅ AI Logger test completed');
      console.log('📊 Performance Stats:', aiLogger.getPerformanceStats());
      console.log('📋 Total Logs:', aiLogger.getLogs().length);
      console.log('🔄 Request Logs:', aiLogger.getRequestLogs().length);
      
    }, 500);
  }, 1000);
};

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).testAILogger = testAILogger;
}
