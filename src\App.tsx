import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import LandingPage from './pages/LandingPage'
import RoadmapGenerator from './pages/RoadmapGenerator'
import RoadmapDisplay from './pages/RoadmapDisplay'
import ModuleLessons from './pages/ModuleLessons'
import { AIDebugPanel } from './components/AIDebugPanel'
import { useAIDebug } from './hooks/useAIDebug'

function App() {
  const { isDebugPanelOpen, toggleDebugPanel } = useAIDebug();

  return (
    <Router>
      <div className="min-h-screen bg-gray-900">
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/generator" element={<RoadmapGenerator />} />
          <Route path="/roadmap" element={<RoadmapDisplay />} />
          <Route path="/module/:moduleId" element={<ModuleLessons />} />
        </Routes>

        {/* AI Debug Panel */}
        <AIDebugPanel
          isOpen={isDebugPanelOpen}
          onToggle={toggleDebugPanel}
        />
      </div>
    </Router>
  )
}

export default App
